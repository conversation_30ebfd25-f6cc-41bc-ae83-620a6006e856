import * as assert from 'assert';
import * as vscode from 'vscode';
import { GeminiClient } from '../services/geminiClient';
import { CodeUtils } from '../services/codeUtils';

suite('Gemini VSCode Agent Extension Tests', () => {
    vscode.window.showInformationMessage('Starting Gemini Agent tests...');

    suite('GeminiClient Tests', () => {
        let client: GeminiClient;

        setup(() => {
            client = new GeminiClient();
        });

        test('Should initialize GeminiClient', () => {
            assert.ok(client);
        });

        test('Should check server health', async () => {
            try {
                const isHealthy = await client.checkHealth();
                // This might fail if server is not running, which is expected
                assert.ok(typeof isHealthy === 'boolean');
            } catch (error) {
                // Expected if server is not running
                assert.ok(true);
            }
        });

        test('Should handle error responses', () => {
            const errorResponse = client['handleError']({
                message: 'Test error'
            });
            
            assert.strictEqual(errorResponse.success, false);
            assert.ok(errorResponse.error);
        });
    });

    suite('CodeUtils Tests', () => {
        let codeUtils: CodeUtils;

        setup(() => {
            codeUtils = new CodeUtils();
        });

        test('Should extract code from markdown response', () => {
            const response = `Here's the improved code:

\`\`\`javascript
function hello() {
    console.log("Hello, World!");
}
\`\`\`

This is much better!`;

            const extractedCode = codeUtils.extractCodeFromResponse(response);
            assert.ok(extractedCode);
            assert.ok(extractedCode.includes('function hello()'));
            assert.ok(extractedCode.includes('console.log'));
        });

        test('Should detect code-like lines', () => {
            const codeLine = 'function test() {';
            const textLine = 'This is just regular text.';
            
            // Access private method for testing
            const looksLikeCode = (codeUtils as any).looksLikeCode;
            
            assert.strictEqual(looksLikeCode(codeLine), true);
            assert.strictEqual(looksLikeCode(textLine), false);
        });

        test('Should format JavaScript code', () => {
            const unformattedCode = `function test(){console.log("test");}`;
            const formattedCode = codeUtils.formatCode(unformattedCode, 'javascript');
            
            assert.ok(formattedCode);
            assert.notStrictEqual(formattedCode, unformattedCode);
        });

        test('Should analyze code complexity', () => {
            const simpleCode = `function hello() {
    console.log("Hello");
}`;
            
            const analysis = codeUtils.analyzeComplexity(simpleCode);
            
            assert.ok(analysis.lines > 0);
            assert.ok(analysis.functions >= 0);
            assert.ok(analysis.classes >= 0);
            assert.ok(['low', 'medium', 'high'].includes(analysis.complexity));
        });

        test('Should validate basic syntax', () => {
            const validCode = `function test() {
    return true;
}`;
            
            const invalidCode = `function test() {
    return true;
`;  // Missing closing brace
            
            const validResult = codeUtils.validateSyntax(validCode, 'javascript');
            const invalidResult = codeUtils.validateSyntax(invalidCode, 'javascript');
            
            assert.strictEqual(validResult.isValid, true);
            assert.strictEqual(invalidResult.isValid, false);
            assert.ok(invalidResult.errors.length > 0);
        });

        test('Should generate documentation', () => {
            const code = `function calculateSum(a, b) {
    return a + b;
}

class Calculator {
    add(x, y) {
        return x + y;
    }
}`;
            
            const documentation = codeUtils.generateDocumentation(code, 'javascript');
            
            assert.ok(documentation.includes('Code Documentation'));
            assert.ok(documentation.includes('javascript'));
            assert.ok(documentation.includes('Functions'));
        });

        test('Should extract function signatures', () => {
            const jsCode = `function test() {}
const arrow = () => {}
function withParams(a, b, c) {}`;
            
            const functions = (codeUtils as any).extractFunctions(jsCode, 'javascript');
            
            assert.ok(Array.isArray(functions));
            assert.ok(functions.length > 0);
        });

        test('Should provide code suggestions', () => {
            const code = `console.`;
            const suggestions = codeUtils.getCodeSuggestions(code, code.length, 'javascript');
            
            assert.ok(Array.isArray(suggestions));
            // Should suggest console methods
            assert.ok(suggestions.some(s => s.includes('log')));
        });
    });

    suite('Extension Commands Tests', () => {
        test('Should register all commands', async () => {
            const commands = await vscode.commands.getCommands(true);
            
            const geminiCommands = [
                'geminiAgent.loginGmail',
                'geminiAgent.chat',
                'geminiAgent.explainSelection',
                'geminiAgent.improveCode',
                'geminiAgent.showDiff',
                'geminiAgent.applyChanges'
            ];
            
            for (const command of geminiCommands) {
                assert.ok(commands.includes(command), `Command ${command} should be registered`);
            }
        });
    });

    suite('Configuration Tests', () => {
        test('Should have default configuration values', () => {
            const config = vscode.workspace.getConfiguration('geminiAgent');
            
            // Test default values
            assert.strictEqual(config.get('autoApply'), false);
            assert.ok(Array.isArray(config.get('languageSupport')));
            assert.strictEqual(config.get('proxyUrl'), 'http://localhost:5000');
            assert.ok(['light', 'dark', 'auto'].includes(config.get('theme') as string));
        });
    });

    suite('Integration Tests', () => {
        test('Should handle missing server gracefully', async () => {
            const client = new GeminiClient();
            
            // This should not throw an error
            const response = await client.sendChatMessage({
                message: 'test'
            }, 'fake-session-id');
            
            assert.strictEqual(response.success, false);
            assert.ok(response.error);
        });
    });
});

// Helper function to create mock VS Code objects
function createMockTextEditor(content: string, languageId: string = 'javascript') {
    return {
        document: {
            getText: () => content,
            languageId: languageId,
            fileName: 'test.js',
            uri: vscode.Uri.file('/test/test.js'),
            positionAt: (offset: number) => new vscode.Position(0, offset)
        },
        selection: new vscode.Selection(0, 0, 0, 0)
    } as any;
}

// Helper function to create mock webview panel
function createMockWebviewPanel() {
    return {
        webview: {
            html: '',
            onDidReceiveMessage: () => ({ dispose: () => {} }),
            postMessage: () => Promise.resolve(true),
            asWebviewUri: (uri: vscode.Uri) => uri
        },
        onDidDispose: () => ({ dispose: () => {} }),
        reveal: () => {},
        dispose: () => {}
    } as any;
}
