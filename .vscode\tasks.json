{"version": "2.0.0", "tasks": [{"label": "extension:npm: compile", "type": "shell", "command": "npm", "args": ["run", "compile"], "options": {"cwd": "${workspaceFolder}/extension"}, "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$tsc"}, {"label": "extension:npm: watch", "type": "shell", "command": "npm", "args": ["run", "watch"], "options": {"cwd": "${workspaceFolder}/extension"}, "group": "build", "presentation": {"echo": true, "reveal": "never", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": "$tsc-watch"}, {"label": "proxy-server:npm: start", "type": "shell", "command": "npm", "args": ["start"], "options": {"cwd": "${workspaceFolder}/proxy-server"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": []}, {"label": "proxy-server:npm: test", "type": "shell", "command": "npm", "args": ["test"], "options": {"cwd": "${workspaceFolder}/proxy-server"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Install All Dependencies", "dependsOrder": "sequence", "dependsOn": ["extension:npm: install", "proxy-server:npm: install"]}, {"label": "extension:npm: install", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/extension"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "proxy-server:npm: install", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/proxy-server"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "Build All", "dependsOrder": "sequence", "dependsOn": ["extension:npm: compile"]}, {"label": "Test All", "dependsOrder": "parallel", "dependsOn": ["proxy-server:npm: test", "extension:npm: test"]}, {"label": "extension:npm: test", "type": "shell", "command": "npm", "args": ["test"], "options": {"cwd": "${workspaceFolder}/extension"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}