import * as vscode from 'vscode';
import axios, { AxiosInstance } from 'axios';

export interface GeminiResponse {
    success: boolean;
    response?: string;
    analysis?: string;
    generatedCode?: string;
    error?: string;
    model?: string;
    timestamp?: string;
}

export interface CodeAnalysisRequest {
    code: string;
    language?: string;
    analysisType: 'explain' | 'improve' | 'debug' | 'refactor' | 'general';
}

export interface ChatRequest {
    message: string;
    context?: string;
    model?: string;
}

export interface CodeGenerationRequest {
    description: string;
    language?: string;
    framework?: string;
    style?: string;
}

export class GeminiClient {
    private httpClient: AxiosInstance;
    private proxyUrl: string;

    constructor() {
        this.updateProxyUrl();
        this.httpClient = this.createHttpClient();
    }

    /**
     * Update proxy URL from configuration
     */
    updateProxyUrl(): void {
        const config = vscode.workspace.getConfiguration('geminiAgent');
        this.proxyUrl = config.get('proxyUrl', 'http://localhost:5000');
        
        if (this.httpClient) {
            this.httpClient = this.createHttpClient();
        }
    }

    /**
     * Create HTTP client with configuration
     */
    private createHttpClient(): AxiosInstance {
        return axios.create({
            baseURL: this.proxyUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
            }
        });
    }

    /**
     * Send chat message to Gemini
     */
    async sendChatMessage(request: ChatRequest, sessionId: string): Promise<GeminiResponse> {
        try {
            const response = await this.httpClient.post('/api/gemini/chat', request, {
                headers: {
                    'X-Session-ID': sessionId
                }
            });

            return response.data;
        } catch (error) {
            return this.handleError(error);
        }
    }

    /**
     * Analyze code with Gemini
     */
    async analyzeCode(request: CodeAnalysisRequest, sessionId: string): Promise<GeminiResponse> {
        try {
            const response = await this.httpClient.post('/api/gemini/analyze-code', request, {
                headers: {
                    'X-Session-ID': sessionId
                }
            });

            return response.data;
        } catch (error) {
            return this.handleError(error);
        }
    }

    /**
     * Generate code with Gemini
     */
    async generateCode(request: CodeGenerationRequest, sessionId: string): Promise<GeminiResponse> {
        try {
            const response = await this.httpClient.post('/api/gemini/generate-code', request, {
                headers: {
                    'X-Session-ID': sessionId
                }
            });

            return response.data;
        } catch (error) {
            return this.handleError(error);
        }
    }

    /**
     * Get available Gemini models
     */
    async getAvailableModels(sessionId: string): Promise<any[]> {
        try {
            const response = await this.httpClient.get('/api/gemini/models', {
                headers: {
                    'X-Session-ID': sessionId
                }
            });

            return response.data.models || [];
        } catch (error) {
            console.error('Failed to get models:', error);
            return [];
        }
    }

    /**
     * Check server health
     */
    async checkHealth(): Promise<boolean> {
        try {
            const response = await this.httpClient.get('/health');
            return response.data.status === 'OK';
        } catch (error) {
            return false;
        }
    }

    /**
     * Handle API errors
     */
    private handleError(error: any): GeminiResponse {
        console.error('Gemini API error:', error);

        if (error.response) {
            // Server responded with error status
            const data = error.response.data;
            return {
                success: false,
                error: data.error || 'Server error',
                ...data
            };
        } else if (error.request) {
            // Request was made but no response received
            return {
                success: false,
                error: 'No response from server. Please check if the proxy server is running.'
            };
        } else {
            // Something else happened
            return {
                success: false,
                error: error.message || 'Unknown error occurred'
            };
        }
    }

    /**
     * Dispose resources
     */
    dispose(): void {
        // Clean up if needed
    }
}
