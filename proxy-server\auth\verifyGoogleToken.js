const { OAuth2Client } = require('google-auth-library');

class GoogleTokenVerifier {
  constructor() {
    this.client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);
    this.sessions = new Map(); // In production, use Redis or database
  }

  /**
   * Verify Google ID token and extract user information
   * @param {string} token - Google ID token
   * @returns {Promise<Object>} User information
   */
  async verifyToken(token) {
    try {
      const ticket = await this.client.verifyIdToken({
        idToken: token,
        audience: process.env.GOOGLE_CLIENT_ID,
      });

      const payload = ticket.getPayload();
      
      if (!payload) {
        throw new Error('Invalid token payload');
      }

      // Extract user information
      const userInfo = {
        userId: payload.sub,
        email: payload.email,
        name: payload.name,
        picture: payload.picture,
        emailVerified: payload.email_verified,
        domain: payload.hd, // Hosted domain (for G Suite accounts)
      };

      // Validate email is verified
      if (!userInfo.emailVerified) {
        throw new Error('Email not verified');
      }

      return userInfo;
    } catch (error) {
      console.error('Token verification failed:', error.message);
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Create a session for the user
   * @param {Object} userInfo - User information from token verification
   * @returns {string} Session ID
   */
  createSession(userInfo) {
    const sessionId = this.generateSessionId();
    const sessionData = {
      ...userInfo,
      createdAt: new Date(),
      lastActivity: new Date(),
      isActive: true
    };

    this.sessions.set(sessionId, sessionData);
    
    // Auto-expire session after 24 hours
    setTimeout(() => {
      this.sessions.delete(sessionId);
    }, 24 * 60 * 60 * 1000);

    return sessionId;
  }

  /**
   * Validate session and return user info
   * @param {string} sessionId - Session ID
   * @returns {Object|null} User information or null if invalid
   */
  validateSession(sessionId) {
    const session = this.sessions.get(sessionId);
    
    if (!session || !session.isActive) {
      return null;
    }

    // Update last activity
    session.lastActivity = new Date();
    this.sessions.set(sessionId, session);

    return session;
  }

  /**
   * Invalidate a session
   * @param {string} sessionId - Session ID to invalidate
   */
  invalidateSession(sessionId) {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.isActive = false;
      this.sessions.set(sessionId, session);
    }
  }

  /**
   * Generate a secure session ID
   * @returns {string} Session ID
   */
  generateSessionId() {
    const crypto = require('crypto');
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Clean up expired sessions
   */
  cleanupExpiredSessions() {
    const now = new Date();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    for (const [sessionId, session] of this.sessions.entries()) {
      if (now - session.lastActivity > maxAge) {
        this.sessions.delete(sessionId);
      }
    }
  }

  /**
   * Get session statistics
   * @returns {Object} Session statistics
   */
  getSessionStats() {
    const activeSessions = Array.from(this.sessions.values())
      .filter(session => session.isActive);

    return {
      totalSessions: this.sessions.size,
      activeSessions: activeSessions.length,
      uniqueUsers: new Set(activeSessions.map(s => s.userId)).size
    };
  }
}

// Create singleton instance
const tokenVerifier = new GoogleTokenVerifier();

// Clean up expired sessions every hour
setInterval(() => {
  tokenVerifier.cleanupExpiredSessions();
}, 60 * 60 * 1000);

module.exports = tokenVerifier;
