/**
 * Test script for Gemini VSCode Agent Proxy Server
 * This script tests the basic functionality of the server
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

// Test configuration
const tests = [
    {
        name: 'Health Check',
        method: 'GET',
        url: '/health',
        expectedStatus: 200
    },
    {
        name: 'Auth Stats',
        method: 'GET',
        url: '/api/auth/stats',
        expectedStatus: 200
    },
    {
        name: 'Invalid Endpoint',
        method: 'GET',
        url: '/invalid-endpoint',
        expectedStatus: 404
    }
];

// Colors for console output
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

function log(color, message) {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

async function runTest(test) {
    try {
        console.log(`\n🧪 Testing: ${test.name}`);
        
        const config = {
            method: test.method,
            url: `${BASE_URL}${test.url}`,
            timeout: 5000,
            validateStatus: () => true // Don't throw on any status code
        };

        if (test.data) {
            config.data = test.data;
        }

        if (test.headers) {
            config.headers = test.headers;
        }

        const response = await axios(config);
        
        const success = response.status === test.expectedStatus;
        
        if (success) {
            log('green', `✅ PASS - Status: ${response.status}`);
            if (response.data) {
                console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
            }
        } else {
            log('red', `❌ FAIL - Expected: ${test.expectedStatus}, Got: ${response.status}`);
            if (response.data) {
                console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
            }
        }
        
        return success;
    } catch (error) {
        log('red', `❌ ERROR - ${error.message}`);
        return false;
    }
}

async function testServerConnection() {
    try {
        console.log(`🔗 Testing connection to ${BASE_URL}...`);
        const response = await axios.get(`${BASE_URL}/health`, { timeout: 3000 });
        log('green', '✅ Server is running and accessible');
        return true;
    } catch (error) {
        log('red', '❌ Cannot connect to server');
        console.log('   Make sure the server is running on port 5000');
        console.log('   Run: npm start or node start.js');
        return false;
    }
}

async function testEnvironmentVariables() {
    console.log('\n🔧 Checking environment configuration...');
    
    try {
        const response = await axios.get(`${BASE_URL}/health`);
        
        if (response.data.status === 'OK') {
            log('green', '✅ Server configuration appears valid');
        } else {
            log('yellow', '⚠️  Server responded but status is not OK');
        }
        
        return true;
    } catch (error) {
        log('red', '❌ Environment configuration issue');
        return false;
    }
}

async function testCORS() {
    console.log('\n🌐 Testing CORS configuration...');
    
    try {
        const response = await axios.options(`${BASE_URL}/health`, {
            headers: {
                'Origin': 'vscode-webview://test',
                'Access-Control-Request-Method': 'GET'
            }
        });
        
        log('green', '✅ CORS appears to be configured');
        return true;
    } catch (error) {
        log('yellow', '⚠️  CORS test inconclusive (this is normal)');
        return true; // CORS errors are expected in this context
    }
}

async function runAllTests() {
    console.log('🚀 Starting Gemini VSCode Agent Proxy Server Tests');
    console.log('=' .repeat(60));
    
    // Test server connection first
    const serverRunning = await testServerConnection();
    if (!serverRunning) {
        log('red', '\n❌ Cannot proceed with tests - server is not running');
        process.exit(1);
    }
    
    // Test environment
    await testEnvironmentVariables();
    
    // Test CORS
    await testCORS();
    
    // Run individual tests
    console.log('\n📋 Running API Tests...');
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
        const result = await runTest(test);
        if (result) {
            passed++;
        } else {
            failed++;
        }
    }
    
    // Summary
    console.log('\n' + '=' .repeat(60));
    console.log('📊 Test Summary:');
    log('green', `✅ Passed: ${passed}`);
    if (failed > 0) {
        log('red', `❌ Failed: ${failed}`);
    }
    
    const total = passed + failed;
    const percentage = total > 0 ? Math.round((passed / total) * 100) : 0;
    
    if (percentage === 100) {
        log('green', `\n🎉 All tests passed! (${percentage}%)`);
    } else if (percentage >= 80) {
        log('yellow', `\n⚠️  Most tests passed (${percentage}%)`);
    } else {
        log('red', `\n❌ Many tests failed (${percentage}%)`);
    }
    
    console.log('\n💡 Next steps:');
    console.log('   1. Test the VS Code extension');
    console.log('   2. Try logging in with Gmail');
    console.log('   3. Test code analysis features');
    
    process.exit(failed > 0 ? 1 : 0);
}

// Handle errors
process.on('unhandledRejection', (error) => {
    log('red', `\n💥 Unhandled error: ${error.message}`);
    process.exit(1);
});

// Run tests
runAllTests().catch((error) => {
    log('red', `\n💥 Test runner error: ${error.message}`);
    process.exit(1);
});
