# دليل البدء السريع - Gemini VSCode Agent

## المشكلة التي واجهتها

زر Google في نافذة تسجيل الدخول لا يعمل لأن Google Client ID غير معد بشكل صحيح.

## الحل

### الخطوة 1: إعداد Google Client ID

1. **اذهب إلى Google Cloud Console**
   - افتح [Google Cloud Console](https://console.cloud.google.com/)
   - سجل الدخول بحساب Google

2. **إنشاء مشروع جديد**
   - انقر "Select a project" → "New Project"
   - اسم المشروع: "Gemini VSCode Agent"
   - انقر "Create"

3. **تفعيل APIs**
   - اذهب إلى "APIs & Services" → "Library"
   - ابحث عن "Google+ API" وفعله

4. **إعداد OAuth Consent Screen**
   - اذهب إلى "APIs & Services" → "OAuth consent screen"
   - اختر "External" → "Create"
   - املأ المعلومات:
     - App name: "Gemini VSCode Agent"
     - User support email: بريدك
     - Developer contact: بريدك
   - احفظ واستمر

5. **إنشاء OAuth Client ID**
   - اذهب إلى "APIs & Services" → "Credentials"
   - "Create Credentials" → "OAuth client ID"
   - اختر "Web application"
   - أضف في "Authorized JavaScript origins":
     - `https://accounts.google.com`
   - انقر "Create"
   - **انسخ Client ID** (يبدأ بـ: `*********-abc.apps.googleusercontent.com`)

### الخطوة 2: إعداد VSCode

1. **فتح الإعدادات**
   - اضغط `Ctrl+,`
   - ابحث عن "geminiAgent.googleClientId"

2. **إدخال Client ID**
   - الصق Client ID في الحقل
   - احفظ الإعدادات

### الخطوة 3: تجربة تسجيل الدخول

1. **تشغيل Extension**
   - اضغط `F5` في VSCode (في مجلد extension)
   - ستفتح نافذة جديدة

2. **تسجيل الدخول**
   - اضغط `Ctrl+Shift+P`
   - ابحث عن "Gemini: Login with Gmail"
   - انقر عليه
   - الآن زر Google سيعمل!

## إذا لم يعمل

### تحقق من:
1. Client ID صحيح ومنسوخ كاملاً
2. Google+ API مفعل
3. OAuth consent screen معد
4. أعد تشغيل VSCode بعد إدخال Client ID

### للمساعدة:
- اضغط F12 في نافذة تسجيل الدخول لرؤية الأخطاء
- تحقق من Output panel في VSCode (View → Output → Gemini Agent)

## ملاحظات مهمة

- Client ID يجب أن يكون من نوع "Web application"
- تأكد من إضافة `https://accounts.google.com` في Authorized origins
- Extension يحتاج إعادة تشغيل بعد إدخال Client ID

الآن زر Google سيعمل بشكل صحيح! 🎉
