{"version": "0.2.0", "configurations": [{"name": "Run Extension", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}/extension"], "outFiles": ["${workspaceFolder}/extension/out/**/*.js"], "preLaunchTask": "${workspaceFolder}/extension:npm: compile"}, {"name": "Extension Tests", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}/extension", "--extensionTestsPath=${workspaceFolder}/extension/out/test/suite/index"], "outFiles": ["${workspaceFolder}/extension/out/test/**/*.js"], "preLaunchTask": "${workspaceFolder}/extension:npm: compile"}, {"name": "Debug Proxy Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/proxy-server/index.js", "cwd": "${workspaceFolder}/proxy-server", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "restart": true, "runtimeExecutable": "node", "skipFiles": ["<node_internals>/**"]}], "compounds": [{"name": "Launch Extension + Server", "configurations": ["Debug Proxy Server", "Run Extension"], "stopAll": true}]}