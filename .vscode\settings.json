{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "files.exclude": {"**/node_modules": true, "**/out": true, "**/.env": true}, "search.exclude": {"**/node_modules": true, "**/out": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/out/**": true}, "eslint.workingDirectories": ["./extension"], "typescript.preferences.quoteStyle": "single", "editor.insertSpaces": true, "editor.tabSize": 4, "editor.detectIndentation": false, "files.eol": "\n", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "editor.formatOnSave": true, "editor.rulers": [100], "geminiAgent.proxyUrl": "http://localhost:5000", "geminiAgent.autoApply": false, "geminiAgent.theme": "auto", "geminiAgent.languageSupport": ["javascript", "typescript", "python", "php", "dart", "java", "csharp", "go", "rust", "cpp", "c"]}