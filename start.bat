@echo off
echo ========================================
echo   Gemini VSCode Agent - Quick Start
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js is available

REM Check if we're in the right directory
if not exist "proxy-server" (
    echo ❌ proxy-server directory not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

if not exist "extension" (
    echo ❌ extension directory not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

echo ✅ Project structure verified

REM Start proxy server
echo.
echo 🚀 Starting Proxy Server...
echo.
cd proxy-server
node start.js

pause
