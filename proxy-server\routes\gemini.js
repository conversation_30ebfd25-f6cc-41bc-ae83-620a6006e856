const express = require('express');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const { requireAuth } = require('./auth');

const router = express.Router();

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);

/**
 * POST /api/gemini/chat
 * Send a message to Gemini and get response
 */
router.post('/chat', requireAuth, async (req, res) => {
  try {
    const { message, context, model = 'gemini-pro' } = req.body;

    if (!message) {
      return res.status(400).json({
        error: 'Message is required',
        code: 'MISSING_MESSAGE'
      });
    }

    // Get the generative model
    const geminiModel = genAI.getGenerativeModel({ model });

    // Prepare the prompt with context if provided
    let prompt = message;
    if (context) {
      prompt = `Context: ${context}\n\nUser Question: ${message}`;
    }

    console.log(`🤖 Gemini request from ${req.user.email}: ${message.substring(0, 100)}...`);

    // Generate response
    const result = await geminiModel.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    res.json({
      success: true,
      response: text,
      model: model,
      timestamp: new Date().toISOString(),
      user: req.user.email
    });

    console.log(`✅ Gemini response sent to ${req.user.email}`);

  } catch (error) {
    console.error('Gemini chat error:', error.message);
    
    // Handle specific Gemini API errors
    if (error.message.includes('API_KEY')) {
      return res.status(500).json({
        error: 'API configuration error',
        code: 'API_KEY_ERROR'
      });
    }

    if (error.message.includes('quota')) {
      return res.status(429).json({
        error: 'API quota exceeded',
        code: 'QUOTA_EXCEEDED'
      });
    }

    res.status(500).json({
      error: 'Failed to get response from Gemini',
      message: error.message,
      code: 'GEMINI_ERROR'
    });
  }
});

/**
 * POST /api/gemini/analyze-code
 * Analyze code with Gemini
 */
router.post('/analyze-code', requireAuth, async (req, res) => {
  try {
    const { code, language, analysisType = 'general' } = req.body;

    if (!code) {
      return res.status(400).json({
        error: 'Code is required',
        code: 'MISSING_CODE'
      });
    }

    const model = genAI.getGenerativeModel({ model: 'gemini-pro' });

    // Create analysis prompt based on type
    let prompt;
    switch (analysisType) {
      case 'explain':
        prompt = `Please explain this ${language || 'code'} in simple terms:\n\n\`\`\`${language || ''}\n${code}\n\`\`\``;
        break;
      case 'improve':
        prompt = `Please suggest improvements for this ${language || 'code'}. Focus on performance, readability, and best practices:\n\n\`\`\`${language || ''}\n${code}\n\`\`\``;
        break;
      case 'debug':
        prompt = `Please help debug this ${language || 'code'} and identify potential issues:\n\n\`\`\`${language || ''}\n${code}\n\`\`\``;
        break;
      case 'refactor':
        prompt = `Please refactor this ${language || 'code'} to make it cleaner and more maintainable:\n\n\`\`\`${language || ''}\n${code}\n\`\`\``;
        break;
      default:
        prompt = `Please analyze this ${language || 'code'}:\n\n\`\`\`${language || ''}\n${code}\n\`\`\``;
    }

    console.log(`🔍 Code analysis request from ${req.user.email}: ${analysisType} for ${language || 'unknown'}`);

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const analysis = response.text();

    res.json({
      success: true,
      analysis,
      analysisType,
      language,
      timestamp: new Date().toISOString(),
      user: req.user.email
    });

    console.log(`✅ Code analysis sent to ${req.user.email}`);

  } catch (error) {
    console.error('Code analysis error:', error.message);
    
    res.status(500).json({
      error: 'Failed to analyze code',
      message: error.message,
      code: 'ANALYSIS_ERROR'
    });
  }
});

/**
 * POST /api/gemini/generate-code
 * Generate code with Gemini
 */
router.post('/generate-code', requireAuth, async (req, res) => {
  try {
    const { description, language, framework, style = 'clean' } = req.body;

    if (!description) {
      return res.status(400).json({
        error: 'Description is required',
        code: 'MISSING_DESCRIPTION'
      });
    }

    const model = genAI.getGenerativeModel({ model: 'gemini-pro' });

    let prompt = `Generate ${language || 'code'} for: ${description}`;
    
    if (framework) {
      prompt += `\nFramework: ${framework}`;
    }
    
    prompt += `\nStyle: ${style} code with comments`;
    prompt += `\nPlease provide only the code with minimal explanation.`;

    console.log(`⚡ Code generation request from ${req.user.email}: ${description}`);

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const generatedCode = response.text();

    res.json({
      success: true,
      generatedCode,
      description,
      language,
      framework,
      timestamp: new Date().toISOString(),
      user: req.user.email
    });

    console.log(`✅ Generated code sent to ${req.user.email}`);

  } catch (error) {
    console.error('Code generation error:', error.message);
    
    res.status(500).json({
      error: 'Failed to generate code',
      message: error.message,
      code: 'GENERATION_ERROR'
    });
  }
});

/**
 * GET /api/gemini/models
 * Get available Gemini models
 */
router.get('/models', requireAuth, async (req, res) => {
  try {
    // Return available models
    const models = [
      {
        name: 'gemini-pro',
        description: 'Best for text-only prompts',
        capabilities: ['text']
      },
      {
        name: 'gemini-pro-vision',
        description: 'Best for text and image prompts',
        capabilities: ['text', 'image']
      }
    ];

    res.json({
      success: true,
      models,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Models list error:', error.message);
    
    res.status(500).json({
      error: 'Failed to get models list',
      message: error.message,
      code: 'MODELS_ERROR'
    });
  }
});

module.exports = router;
