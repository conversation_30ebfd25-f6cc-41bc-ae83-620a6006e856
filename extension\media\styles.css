/* Gemini VSCode Agent Styles */

:root {
    --primary-color: #4285f4;
    --secondary-color: #34a853;
    --danger-color: #ea4335;
    --warning-color: #fbbc04;
    --background-color: var(--vscode-editor-background);
    --foreground-color: var(--vscode-editor-foreground);
    --border-color: var(--vscode-panel-border);
    --input-background: var(--vscode-input-background);
    --input-foreground: var(--vscode-input-foreground);
    --button-background: var(--vscode-button-background);
    --button-foreground: var(--vscode-button-foreground);
    --button-hover-background: var(--vscode-button-hoverBackground);
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-font-size);
    color: var(--foreground-color);
    background-color: var(--background-color);
    line-height: 1.6;
}

.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    background: var(--vscode-editor-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 500px;
}

.login-card h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.login-card p {
    margin-bottom: 30px;
    color: var(--vscode-descriptionForeground);
    font-size: 1.1em;
}

.g_id_signin {
    margin: 20px 0;
    display: flex;
    justify-content: center;
}

.features {
    margin-top: 40px;
    text-align: left;
}

.features h3 {
    color: var(--foreground-color);
    margin-bottom: 15px;
    font-size: 1.2em;
}

.features ul {
    list-style: none;
    padding: 0;
}

.features li {
    padding: 8px 0;
    color: var(--vscode-descriptionForeground);
    font-size: 0.95em;
}

.features li::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: var(--secondary-color);
    border-radius: 50%;
    margin-right: 10px;
}

/* Chat Panel Styles */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-height: 600px;
}

.chat-header {
    background: var(--vscode-titleBar-activeBackground);
    color: var(--vscode-titleBar-activeForeground);
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chat-header h2 {
    font-size: 1.2em;
    margin: 0;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9em;
}

.user-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: var(--background-color);
}

.message {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    flex-shrink: 0;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 12px;
    background: var(--vscode-editor-selectionBackground);
    border: 1px solid var(--border-color);
}

.message.user .message-content {
    background: var(--primary-color);
    color: white;
}

.message-time {
    font-size: 0.8em;
    color: var(--vscode-descriptionForeground);
    margin-top: 5px;
}

.chat-input-container {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    background: var(--vscode-editor-background);
}

.chat-input-row {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.chat-input {
    flex: 1;
    min-height: 40px;
    max-height: 120px;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    background: var(--input-background);
    color: var(--input-foreground);
    resize: none;
    font-family: inherit;
    font-size: inherit;
    outline: none;
}

.chat-input:focus {
    border-color: var(--primary-color);
}

.send-button {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.send-button:hover {
    background: #3367d6;
}

.send-button:disabled {
    background: var(--vscode-button-secondaryBackground);
    cursor: not-allowed;
}

/* Code blocks */
.code-block {
    background: var(--vscode-textCodeBlock-background);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 15px;
    margin: 10px 0;
    font-family: var(--vscode-editor-font-family);
    font-size: 0.9em;
    overflow-x: auto;
}

.code-block pre {
    margin: 0;
    white-space: pre-wrap;
}

/* Diff View Styles */
.diff-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.diff-header {
    background: var(--vscode-titleBar-activeBackground);
    color: var(--vscode-titleBar-activeForeground);
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.diff-actions {
    display: flex;
    gap: 10px;
}

.diff-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.diff-side {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.diff-side-header {
    background: var(--vscode-editorGroupHeader-tabsBackground);
    padding: 10px 15px;
    border-bottom: 1px solid var(--border-color);
    font-weight: bold;
    font-size: 0.9em;
}

.diff-side.original .diff-side-header {
    background: #ffeaea;
    color: #d73a49;
}

.diff-side.modified .diff-side-header {
    background: #e6ffed;
    color: #28a745;
}

.diff-editor {
    flex: 1;
    overflow: auto;
    padding: 15px;
    font-family: var(--vscode-editor-font-family);
    font-size: var(--vscode-editor-font-size);
    line-height: 1.4;
    white-space: pre-wrap;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    font-family: inherit;
    transition: background-color 0.2s;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #3367d6;
}

.btn-secondary {
    background: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
}

.btn-secondary:hover {
    background: var(--vscode-button-secondaryHoverBackground);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #d33b2c;
}

/* Loading spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--vscode-progressBar-background);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .login-card {
        padding: 30px 20px;
    }
    
    .diff-content {
        flex-direction: column;
    }
    
    .diff-side {
        min-height: 300px;
    }
}
