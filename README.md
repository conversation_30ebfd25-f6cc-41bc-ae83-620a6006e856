# Gemini VSCode Agent

إضافة VS Code ذكية تستخدم Google Gemini AI مع تسجيل دخول Gmail آمن.

## 🌟 المميزات

- **تسجيل دخول Gmail آمن**: OAuth 2.0 مع Google
- **ذكاء اصطناعي متقدم**: Google Gemini API
- **خادم داخلي آمن**: لا يتم تضمين API Keys في الإضافة
- **واجهة مستخدم متقدمة**: WebViews تفاعلية
- **تحليل وتحسين الكود**: اقتراحات ذكية وتعديلات تلقائية
- **عرض الفروقات**: مقارنة التعديلات قبل التطبيق

## 🏗️ هيكل المشروع

```
gemini-vscode-agent/
├── extension/                   # إضافة VS Code
│   ├── src/
│   │   ├── extension.ts        # نقطة الدخول
│   │   ├── agent/              # Agent الذكي
│   │   ├── services/           # خدمات الاتصال
│   │   └── webviews/           # واجهات المستخدم
│   ├── media/                  # الأصول والتصميم
│   └── package.json
│
├── proxy-server/               # الخادم الداخلي
│   ├── index.js               # خادم Express
│   ├── routes/                # مسارات API
│   ├── auth/                  # التحقق من الهوية
│   └── .env                   # متغيرات البيئة
│
└── README.md
```

## 🚀 التثبيت والتشغيل

### 1. إعداد الخادم الداخلي

```bash
cd proxy-server
npm install
# إنشاء ملف .env وإضافة Google API Key
npm start
```

### 2. تثبيت الإضافة

```bash
cd extension
npm install
npm run compile
# فتح VS Code وتحميل الإضافة في وضع التطوير
```

## 🔧 الإعداد

1. الحصول على Google API Key من Google Cloud Console
2. إعداد OAuth 2.0 Client ID
3. إضافة المتغيرات في ملف `.env`

## 📋 الأوامر المتاحة

- `Gemini: Login with Gmail` - تسجيل الدخول
- `Gemini: Open Chat Panel` - فتح لوحة المحادثة
- `Gemini: Explain Selected Code` - تفسير الكود المحدد
- `Gemini: Improve Current File` - تحسين الملف الحالي
- `Gemini: Preview Changes` - عرض التعديلات المقترحة

## 🔐 الأمان

- لا يتم تخزين API Keys في الإضافة
- التحقق من Gmail Token في الخادم
- جلسات آمنة ومشفرة
- عدم تخزين بيانات حساسة محلياً

## 🛠️ التطوير

هذا المشروع في مرحلة التطوير النشط. المساهمات مرحب بها!

## 📄 الترخيص

MIT License
