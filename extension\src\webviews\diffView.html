<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Diff - Gemini Suggestions</title>
    <link href="{{STYLES_URI}}" rel="stylesheet">
    <style>
        .diff-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: var(--vscode-editor-background);
        }

        .diff-header {
            background: var(--vscode-titleBar-activeBackground);
            color: var(--vscode-titleBar-activeForeground);
            padding: 15px 20px;
            border-bottom: 1px solid var(--vscode-panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        .header-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .file-icon {
            font-size: 1.2em;
        }

        .file-name {
            font-weight: 600;
            font-size: 1.1em;
        }

        .diff-stats {
            font-size: 0.9em;
            color: var(--vscode-descriptionForeground);
        }

        .diff-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
        }

        .btn-primary:hover {
            background: var(--vscode-button-hoverBackground);
        }

        .btn-secondary {
            background: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }

        .btn-secondary:hover {
            background: var(--vscode-button-secondaryHoverBackground);
        }

        .btn-danger {
            background: #d73a49;
            color: white;
        }

        .btn-danger:hover {
            background: #cb2431;
        }

        .diff-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .diff-side {
            flex: 1;
            display: flex;
            flex-direction: column;
            border-right: 1px solid var(--vscode-panel-border);
        }

        .diff-side:last-child {
            border-right: none;
        }

        .diff-side-header {
            background: var(--vscode-editorGroupHeader-tabsBackground);
            padding: 12px 16px;
            border-bottom: 1px solid var(--vscode-panel-border);
            font-weight: 600;
            font-size: 0.9em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .diff-side.original .diff-side-header {
            background: #ffeaea;
            color: #d73a49;
        }

        .diff-side.modified .diff-side-header {
            background: #e6ffed;
            color: #28a745;
        }

        .side-icon {
            font-size: 1em;
        }

        .diff-editor {
            flex: 1;
            overflow: auto;
            font-family: var(--vscode-editor-font-family);
            font-size: var(--vscode-editor-font-size);
            line-height: 1.4;
            background: var(--vscode-editor-background);
        }

        .line-numbers {
            background: var(--vscode-editorLineNumber-background);
            color: var(--vscode-editorLineNumber-foreground);
            padding: 0 8px;
            text-align: right;
            user-select: none;
            min-width: 40px;
            border-right: 1px solid var(--vscode-panel-border);
        }

        .code-content {
            flex: 1;
            padding: 0 12px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .code-line {
            display: flex;
            min-height: 20px;
            position: relative;
        }

        .code-line.added {
            background: rgba(40, 167, 69, 0.1);
        }

        .code-line.removed {
            background: rgba(215, 58, 73, 0.1);
        }

        .code-line.modified {
            background: rgba(255, 193, 7, 0.1);
        }

        .code-line:hover {
            background: var(--vscode-list-hoverBackground);
        }

        .line-marker {
            position: absolute;
            left: -4px;
            width: 4px;
            height: 100%;
        }

        .code-line.added .line-marker {
            background: #28a745;
        }

        .code-line.removed .line-marker {
            background: #d73a49;
        }

        .code-line.modified .line-marker {
            background: #ffc107;
        }

        .diff-summary {
            background: var(--vscode-editor-selectionBackground);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            padding: 16px;
            margin: 16px;
            flex-shrink: 0;
        }

        .summary-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--vscode-foreground);
        }

        .summary-stats {
            display: flex;
            gap: 20px;
            font-size: 0.9em;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .stat-added {
            color: #28a745;
        }

        .stat-removed {
            color: #d73a49;
        }

        .stat-modified {
            color: #ffc107;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-content {
            background: var(--vscode-editor-background);
            padding: 20px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid var(--vscode-panel-border);
        }

        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid var(--vscode-progressBar-background);
            border-top: 3px solid var(--vscode-button-background);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .diff-content {
                flex-direction: column;
            }
            
            .diff-side {
                min-height: 300px;
                border-right: none;
                border-bottom: 1px solid var(--vscode-panel-border);
            }
            
            .diff-side:last-child {
                border-bottom: none;
            }
            
            .diff-actions {
                flex-wrap: wrap;
                gap: 8px;
            }
            
            .action-btn {
                flex: 1;
                min-width: 100px;
            }
        }

        /* Scrollbar styling */
        .diff-editor::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .diff-editor::-webkit-scrollbar-track {
            background: var(--vscode-scrollbarSlider-background);
        }

        .diff-editor::-webkit-scrollbar-thumb {
            background: var(--vscode-scrollbarSlider-hoverBackground);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="diff-container">
        <div class="diff-header">
            <div class="header-info">
                <span class="file-icon">📄</span>
                <span class="file-name" id="fileName">Loading...</span>
                <span class="diff-stats" id="diffStats"></span>
            </div>
            <div class="diff-actions">
                <button class="action-btn btn-secondary" onclick="rejectChanges()">
                    ❌ Reject
                </button>
                <button class="action-btn btn-primary" onclick="applyChanges()">
                    ✅ Apply Changes
                </button>
            </div>
        </div>

        <div class="diff-summary" id="diffSummary">
            <div class="summary-title">📊 Change Summary</div>
            <div class="summary-stats" id="summaryStats">
                <div class="stat-item stat-added">
                    <span>+</span>
                    <span id="addedLines">0</span>
                    <span>added</span>
                </div>
                <div class="stat-item stat-removed">
                    <span>-</span>
                    <span id="removedLines">0</span>
                    <span>removed</span>
                </div>
                <div class="stat-item stat-modified">
                    <span>~</span>
                    <span id="modifiedLines">0</span>
                    <span>modified</span>
                </div>
            </div>
        </div>

        <div class="diff-content">
            <div class="diff-side original">
                <div class="diff-side-header">
                    <span class="side-icon">📝</span>
                    <span>Original Code</span>
                </div>
                <div class="diff-editor" id="originalEditor">
                    <div id="originalContent"></div>
                </div>
            </div>

            <div class="diff-side modified">
                <div class="diff-side-header">
                    <span class="side-icon">✨</span>
                    <span>Improved Code</span>
                </div>
                <div class="diff-editor" id="modifiedEditor">
                    <div id="modifiedContent"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <div>Processing changes...</div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let originalCode = '';
        let modifiedCode = '';
        let fileName = '';

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            // Request initial data from extension
            vscode.postMessage({ command: 'getDiffData' });
        });

        function setDiffData(data) {
            originalCode = data.original || '';
            modifiedCode = data.modified || '';
            fileName = data.fileName || 'Untitled';
            
            document.getElementById('fileName').textContent = fileName;
            
            renderDiff();
            calculateStats();
        }

        function renderDiff() {
            const originalLines = originalCode.split('\n');
            const modifiedLines = modifiedCode.split('\n');
            
            const originalContainer = document.getElementById('originalContent');
            const modifiedContainer = document.getElementById('modifiedContent');
            
            // Clear existing content
            originalContainer.innerHTML = '';
            modifiedContainer.innerHTML = '';
            
            // Simple line-by-line comparison
            const maxLines = Math.max(originalLines.length, modifiedLines.length);
            
            for (let i = 0; i < maxLines; i++) {
                const originalLine = originalLines[i] || '';
                const modifiedLine = modifiedLines[i] || '';
                
                // Determine line type
                let lineType = '';
                if (i >= originalLines.length) {
                    lineType = 'added';
                } else if (i >= modifiedLines.length) {
                    lineType = 'removed';
                } else if (originalLine !== modifiedLine) {
                    lineType = 'modified';
                }
                
                // Create original line
                const originalLineEl = createCodeLine(originalLine, i + 1, lineType === 'added' ? '' : lineType);
                originalContainer.appendChild(originalLineEl);
                
                // Create modified line
                const modifiedLineEl = createCodeLine(modifiedLine, i + 1, lineType === 'removed' ? '' : lineType);
                modifiedContainer.appendChild(modifiedLineEl);
            }
        }

        function createCodeLine(content, lineNumber, type) {
            const lineEl = document.createElement('div');
            lineEl.className = `code-line ${type}`;
            
            const lineNumberEl = document.createElement('div');
            lineNumberEl.className = 'line-numbers';
            lineNumberEl.textContent = lineNumber;
            
            const contentEl = document.createElement('div');
            contentEl.className = 'code-content';
            contentEl.textContent = content || ' '; // Ensure empty lines have space
            
            const markerEl = document.createElement('div');
            markerEl.className = 'line-marker';
            
            lineEl.appendChild(markerEl);
            lineEl.appendChild(lineNumberEl);
            lineEl.appendChild(contentEl);
            
            return lineEl;
        }

        function calculateStats() {
            const originalLines = originalCode.split('\n');
            const modifiedLines = modifiedCode.split('\n');
            
            let added = 0;
            let removed = 0;
            let modified = 0;
            
            const maxLines = Math.max(originalLines.length, modifiedLines.length);
            
            for (let i = 0; i < maxLines; i++) {
                const originalLine = originalLines[i];
                const modifiedLine = modifiedLines[i];
                
                if (originalLine === undefined) {
                    added++;
                } else if (modifiedLine === undefined) {
                    removed++;
                } else if (originalLine !== modifiedLine) {
                    modified++;
                }
            }
            
            // Update UI
            document.getElementById('addedLines').textContent = added;
            document.getElementById('removedLines').textContent = removed;
            document.getElementById('modifiedLines').textContent = modified;
            
            const totalChanges = added + removed + modified;
            document.getElementById('diffStats').textContent = `${totalChanges} changes`;
        }

        function applyChanges() {
            if (confirm('Are you sure you want to apply these changes? This will modify your current file.')) {
                showLoading();
                
                vscode.postMessage({
                    command: 'applyChanges',
                    code: modifiedCode
                });
            }
        }

        function rejectChanges() {
            if (confirm('Are you sure you want to reject these changes?')) {
                vscode.postMessage({
                    command: 'rejectChanges'
                });
            }
        }

        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.command) {
                case 'setDiffData':
                    setDiffData(message.data);
                    break;
                case 'changesApplied':
                    hideLoading();
                    // Could show success message or close panel
                    break;
                case 'changesRejected':
                    hideLoading();
                    // Could show rejection message or close panel
                    break;
                case 'error':
                    hideLoading();
                    alert('Error: ' + message.error);
                    break;
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'Enter':
                        e.preventDefault();
                        applyChanges();
                        break;
                    case 'Escape':
                        e.preventDefault();
                        rejectChanges();
                        break;
                }
            }
        });
    </script>
</body>
</html>
