{"name": "gemini-vscode-proxy-server", "version": "1.0.0", "description": "Proxy server for Gemini VSCode Agent extension", "main": "index.js", "scripts": {"start": "node start.js", "dev": "nodemon index.js", "test": "node test.js", "server": "node index.js"}, "keywords": ["gemini", "vscode", "ai", "proxy", "google"], "author": "Gemini VSCode Agent", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "@google/generative-ai": "^0.2.1", "google-auth-library": "^9.4.0", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}