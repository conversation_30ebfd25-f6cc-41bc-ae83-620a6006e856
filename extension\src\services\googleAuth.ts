import axios from 'axios';
import * as vscode from 'vscode';

export interface UserInfo {
    id: string;
    email: string;
    name: string;
    picture: string;
    domain?: string;
}

export interface AuthSession {
    sessionId: string;
    user: UserInfo;
    expiresAt: Date;
}

export class GoogleAuthService {
    private context: vscode.ExtensionContext;
    private currentSession: AuthSession | null = null;
    private loginPanel: vscode.WebviewPanel | null = null;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.loadStoredSession();
    }

    /**
     * Initiate Gmail login process
     */
    async login(): Promise<AuthSession> {
        return new Promise((resolve, reject) => {
            try {
                // Create webview panel for login
                this.loginPanel = vscode.window.createWebviewPanel(
                    'geminiLogin',
                    'Login with Gmail',
                    vscode.ViewColumn.One,
                    {
                        enableScripts: true,
                        retainContextWhenHidden: true,
                        localResourceRoots: [
                            vscode.Uri.joinPath(this.context.extensionUri, 'src', 'webviews'),
                            vscode.Uri.joinPath(this.context.extensionUri, 'media')
                        ]
                    }
                );

                // Set webview content
                this.loginPanel.webview.html = this.getLoginHtml();

                // Handle messages from webview
                this.loginPanel.webview.onDidReceiveMessage(
                    async (message) => {
                        switch (message.command) {
                            case 'loginSuccess':
                                try {
                                    const session = await this.handleLoginSuccess(message.data);
                                    this.loginPanel?.dispose();
                                    this.loginPanel = null;
                                    resolve(session);
                                } catch (error) {
                                    reject(error);
                                }
                                break;
                            case 'loginError':
                                this.loginPanel?.dispose();
                                this.loginPanel = null;
                                reject(new Error(message.error));
                                break;
                            case 'loginCancel':
                                this.loginPanel?.dispose();
                                this.loginPanel = null;
                                reject(new Error('Login cancelled by user'));
                                break;
                        }
                    }
                );

                // Handle panel disposal
                this.loginPanel.onDidDispose(() => {
                    this.loginPanel = null;
                    reject(new Error('Login panel was closed'));
                });

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Handle successful login from webview
     */
    private async handleLoginSuccess(data: any): Promise<AuthSession> {
        try {
            const { idToken } = data;

            if (!idToken) {
                throw new Error('No ID token received');
            }

            // Send token to proxy server for verification
            const config = vscode.workspace.getConfiguration('geminiAgent');
            const proxyUrl = config.get('proxyUrl', 'http://localhost:5000');

            const response = await axios.post(`${proxyUrl}/api/auth/login`, {
                idToken
            });

            const result = response.data;

            // Create session object
            const session: AuthSession = {
                sessionId: result.sessionId,
                user: result.user,
                expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
            };

            // Store session
            this.currentSession = session;
            await this.storeSession(session);

            vscode.window.showInformationMessage(`Welcome, ${session.user.name}! You are now logged in.`);

            return session;

        } catch (error) {
            console.error('Login handling error:', error);
            throw error;
        }
    }

    /**
     * Get current authentication session
     */
    getCurrentSession(): AuthSession | null {
        if (this.currentSession && this.currentSession.expiresAt > new Date()) {
            return this.currentSession;
        }
        return null;
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated(): boolean {
        return this.getCurrentSession() !== null;
    }

    /**
     * Logout user
     */
    async logout(): Promise<void> {
        try {
            if (this.currentSession) {
                // Notify proxy server
                const config = vscode.workspace.getConfiguration('geminiAgent');
                const proxyUrl = config.get('proxyUrl', 'http://localhost:5000');

                await axios.post(`${proxyUrl}/api/auth/logout`, {
                    sessionId: this.currentSession.sessionId
                });
            }

            // Clear local session
            this.currentSession = null;
            await this.context.globalState.update('gemini.session', undefined);

            vscode.window.showInformationMessage('You have been logged out successfully.');

        } catch (error) {
            console.error('Logout error:', error);
            // Clear local session even if server request fails
            this.currentSession = null;
            await this.context.globalState.update('gemini.session', undefined);
        }
    }

    /**
     * Store session in VS Code global state
     */
    private async storeSession(session: AuthSession): Promise<void> {
        await this.context.globalState.update('gemini.session', {
            sessionId: session.sessionId,
            user: session.user,
            expiresAt: session.expiresAt.toISOString()
        });
    }

    /**
     * Load stored session from VS Code global state
     */
    private loadStoredSession(): void {
        const stored = this.context.globalState.get('gemini.session') as any;

        if (stored && stored.sessionId && stored.expiresAt) {
            const expiresAt = new Date(stored.expiresAt);

            if (expiresAt > new Date()) {
                this.currentSession = {
                    sessionId: stored.sessionId,
                    user: stored.user,
                    expiresAt
                };
            }
        }
    }

    /**
     * Get HTML content for login webview
     */
    private getLoginHtml(): string {
        const styleUri = this.loginPanel?.webview.asWebviewUri(
            vscode.Uri.joinPath(this.context.extensionUri, 'media', 'styles.css')
        );

        // Try to load the HTML template
        const fs = require('fs');
        const path = require('path');

        try {
            const htmlPath = path.join(this.context.extensionPath, 'src', 'webviews', 'login.html');
            let html = fs.readFileSync(htmlPath, 'utf8');

            // Replace placeholders
            html = html.replace('{{STYLES_URI}}', styleUri?.toString() || '');
            html = html.replace('{{GOOGLE_CLIENT_ID}}', 'YOUR_GOOGLE_CLIENT_ID'); // This should be configured

            return html;
        } catch (error) {
            console.error('Failed to load login HTML template:', error);
            return this.getFallbackLoginHtml(styleUri);
        }
    }

    /**
     * Fallback login HTML if template loading fails
     */
    private getFallbackLoginHtml(styleUri?: vscode.Uri): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Login with Gmail</title>
            <link href="${styleUri}" rel="stylesheet">
            <script src="https://accounts.google.com/gsi/client" async defer></script>
        </head>
        <body>
            <div class="container">
                <div class="login-card">
                    <h1>🤖 Gemini VSCode Agent</h1>
                    <p>Sign in with your Gmail account to access AI-powered coding assistance</p>

                    <div id="g_id_onload"
                         data-client_id="YOUR_GOOGLE_CLIENT_ID"
                         data-callback="handleCredentialResponse"
                         data-auto_prompt="false">
                    </div>

                    <div class="g_id_signin"
                         data-type="standard"
                         data-size="large"
                         data-theme="outline"
                         data-text="sign_in_with"
                         data-shape="rectangular"
                         data-logo_alignment="left">
                    </div>

                    <div class="features">
                        <h3>Features:</h3>
                        <ul>
                            <li>🧠 AI-powered code analysis</li>
                            <li>💡 Smart suggestions and improvements</li>
                            <li>🔍 Code explanation and documentation</li>
                            <li>🚀 Automated refactoring</li>
                        </ul>
                    </div>
                </div>
            </div>

            <script>
                const vscode = acquireVsCodeApi();

                function handleCredentialResponse(response) {
                    try {
                        vscode.postMessage({
                            command: 'loginSuccess',
                            data: {
                                idToken: response.credential
                            }
                        });
                    } catch (error) {
                        vscode.postMessage({
                            command: 'loginError',
                            error: error.message
                        });
                    }
                }

                // Handle errors
                window.addEventListener('error', (event) => {
                    vscode.postMessage({
                        command: 'loginError',
                        error: event.error.message
                    });
                });
            </script>
        </body>
        </html>`;
    }

    /**
     * Dispose resources
     */
    dispose(): void {
        if (this.loginPanel) {
            this.loginPanel.dispose();
            this.loginPanel = null;
        }
    }
}
