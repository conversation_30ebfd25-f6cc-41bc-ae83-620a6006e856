# تعليمات التثبيت - Gemini VSCode Agent

## 📋 المتطلبات الأساسية

### 1. Node.js
- تحميل وتثبيت Node.js من [nodejs.org](https://nodejs.org/)
- الإصدار المطلوب: 16.0.0 أو أحدث
- للتحقق من التثبيت: `node --version`

### 2. VS Code
- تحميل وتثبيت VS Code من [code.visualstudio.com](https://code.visualstudio.com/)
- الإصدار المطلوب: 1.74.0 أو أحدث

### 3. Google Cloud Console
- إنشاء مشروع في [Google Cloud Console](https://console.cloud.google.com/)
- تفعيل Gemini API
- إنشاء OAuth 2.0 Client ID
- الحصول على API Key

## 🚀 خطوات التثبيت

### الخطوة 1: إعداد Google Cloud

1. **إنشاء مشروع جديد**
   - اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
   - انقر على "New Project"
   - أدخل اسم المشروع واختر المنظمة

2. **تفعيل APIs المطلوبة**
   ```
   - Generative Language API (Gemini)
   - Google Sign-In API
   ```

3. **إنشاء API Key**
   - اذهب إلى "APIs & Services" > "Credentials"
   - انقر على "Create Credentials" > "API Key"
   - احفظ API Key في مكان آمن

4. **إنشاء OAuth 2.0 Client**
   - في نفس صفحة Credentials
   - انقر على "Create Credentials" > "OAuth 2.0 Client ID"
   - اختر "Web application"
   - أضف Authorized redirect URIs:
     ```
     http://localhost:5000/auth/callback
     vscode-webview://
     ```

### الخطوة 2: إعداد الخادم الداخلي

1. **الانتقال إلى مجلد الخادم**
   ```bash
   cd proxy-server
   ```

2. **تثبيت المكتبات**
   ```bash
   npm install
   ```

3. **إعداد متغيرات البيئة**
   - انسخ ملف `.env.example` إلى `.env`
   - أضف المعلومات المطلوبة:
   ```env
   GOOGLE_API_KEY=your_api_key_here
   GOOGLE_CLIENT_ID=your_client_id_here
   GOOGLE_CLIENT_SECRET=your_client_secret_here
   PORT=5000
   NODE_ENV=development
   ```

4. **تشغيل الخادم**
   ```bash
   npm start
   # أو
   node start.js
   ```

### الخطوة 3: إعداد إضافة VS Code

1. **الانتقال إلى مجلد الإضافة**
   ```bash
   cd extension
   ```

2. **تثبيت المكتبات**
   ```bash
   npm install
   ```

3. **تجميع TypeScript**
   ```bash
   npm run compile
   ```

4. **تشغيل في وضع التطوير**
   - افتح VS Code
   - اضغط `F5` أو اذهب إلى "Run" > "Start Debugging"
   - سيفتح نافذة VS Code جديدة مع الإضافة محملة

### الخطوة 4: اختبار النظام

1. **تشغيل الخادم**
   - تأكد من تشغيل الخادم على المنفذ 5000
   - تحقق من الصحة: `http://localhost:5000/health`

2. **اختبار الإضافة**
   - افتح Command Palette (`Ctrl+Shift+P`)
   - ابحث عن "Gemini: Login with Gmail"
   - اتبع عملية تسجيل الدخول

3. **اختبار الوظائف**
   - فتح ملف كود
   - تحديد جزء من الكود
   - استخدام "Gemini: Explain Selected Code"

## 🛠️ التشغيل السريع

### Windows
```cmd
start.bat
```

### Linux/Mac
```bash
chmod +x start.sh
./start.sh
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في API Key**
   ```
   Error: API_KEY_ERROR
   ```
   - تحقق من صحة API Key في ملف .env
   - تأكد من تفعيل Gemini API في Google Cloud

2. **خطأ في OAuth**
   ```
   Error: Authentication failed
   ```
   - تحقق من Client ID و Client Secret
   - تأكد من إضافة Redirect URIs الصحيحة

3. **خطأ في الاتصال**
   ```
   Error: No response from server
   ```
   - تأكد من تشغيل الخادم على المنفذ 5000
   - تحقق من إعدادات Firewall

4. **خطأ في تجميع TypeScript**
   ```
   Error: Cannot find module
   ```
   - تشغيل `npm install` في مجلد extension
   - تشغيل `npm run compile`

### سجلات الأخطاء:

- **سجلات الخادم**: تظهر في terminal حيث يعمل الخادم
- **سجلات الإضافة**: في VS Code Developer Console (`Help` > `Toggle Developer Tools`)

## 📚 الاستخدام

### الأوامر المتاحة:

1. **تسجيل الدخول**
   ```
   Gemini: Login with Gmail
   ```

2. **فتح المحادثة**
   ```
   Gemini: Open Chat Panel
   ```

3. **تفسير الكود**
   ```
   Gemini: Explain Selected Code
   ```

4. **تحسين الكود**
   ```
   Gemini: Improve Current File
   ```

5. **عرض التعديلات**
   ```
   Gemini: Preview Suggested Changes
   ```

### نصائح للاستخدام:

- استخدم تحديد دقيق للكود للحصول على تفسيرات أفضل
- راجع التعديلات المقترحة قبل تطبيقها
- استخدم المحادثة للأسئلة العامة حول البرمجة

## 🔒 الأمان

- لا تشارك API Keys مع أحد
- استخدم HTTPS في الإنتاج
- راجع الأذونات المطلوبة للإضافة
- احتفظ بنسخة احتياطية من إعداداتك

## 📞 الدعم

إذا واجهت مشاكل:

1. تحقق من سجلات الأخطاء
2. راجع الوثائق
3. ابحث في المشاكل المعروفة
4. أنشئ issue جديد مع تفاصيل المشكلة

---

**ملاحظة**: هذا المشروع في مرحلة التطوير. قد تحتاج بعض الوظائف إلى تحسينات إضافية.
