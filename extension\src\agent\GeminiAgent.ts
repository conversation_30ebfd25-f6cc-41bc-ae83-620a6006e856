import * as vscode from 'vscode';
import { CodeUtils } from '../services/codeUtils';
import { ChatRequest, CodeAnalysisRequest, GeminiClient } from '../services/geminiClient';
import { GoogleAuthService } from '../services/googleAuth';

export class GeminiAgent {
    private context: vscode.ExtensionContext;
    private authService: GoogleAuthService;
    private geminiClient: GeminiClient;
    private codeUtils: CodeUtils;
    private chatPanel: vscode.WebviewPanel | null = null;
    private diffPanel: vscode.WebviewPanel | null = null;
    private pendingChanges: Map<string, string> = new Map();
    private autoSuggestTimeout: NodeJS.Timeout | null = null;

    constructor(
        context: vscode.ExtensionContext,
        authService: GoogleAuthService,
        geminiClient: GeminiClient
    ) {
        this.context = context;
        this.authService = authService;
        this.geminiClient = geminiClient;
        this.codeUtils = new CodeUtils();
    }

    /**
     * Login with Gmail
     */
    async loginWithGmail(): Promise<void> {
        try {
            vscode.window.showInformationMessage('Opening Gmail login...');
            const session = await this.authService.login();

            if (session) {
                vscode.window.showInformationMessage(
                    `Welcome ${session.user.name}! You can now use Gemini AI features.`,
                    'Open Chat'
                ).then(selection => {
                    if (selection === 'Open Chat') {
                        this.openChatPanel();
                    }
                });
            }
        } catch (error) {
            console.error('Login error:', error);
            vscode.window.showErrorMessage(`Login failed: ${error}`);
        }
    }

    /**
     * Open chat panel
     */
    async openChatPanel(): Promise<void> {
        if (!this.authService.isAuthenticated()) {
            const result = await vscode.window.showWarningMessage(
                'Please login with Gmail first to use Gemini features.',
                'Login Now'
            );
            if (result === 'Login Now') {
                await this.loginWithGmail();
                return;
            }
            return;
        }

        if (this.chatPanel) {
            this.chatPanel.reveal();
            return;
        }

        this.chatPanel = vscode.window.createWebviewPanel(
            'geminiChat',
            'Gemini AI Chat',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(this.context.extensionUri, 'src', 'webviews'),
                    vscode.Uri.joinPath(this.context.extensionUri, 'media')
                ]
            }
        );

        this.chatPanel.webview.html = this.getChatPanelHtml();
        this.setupChatPanelMessageHandling();

        this.chatPanel.onDidDispose(() => {
            this.chatPanel = null;
        });
    }

    /**
     * Explain selected code
     */
    async explainSelectedCode(): Promise<void> {
        if (!this.authService.isAuthenticated()) {
            vscode.window.showWarningMessage('Please login first.');
            return;
        }

        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found.');
            return;
        }

        const selection = editor.selection;
        const selectedText = editor.document.getText(selection);

        if (!selectedText.trim()) {
            vscode.window.showWarningMessage('Please select some code to explain.');
            return;
        }

        const language = editor.document.languageId;
        const session = this.authService.getCurrentSession();

        if (!session) {
            vscode.window.showWarningMessage('Session expired. Please login again.');
            return;
        }

        try {
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Explaining code with Gemini...',
                cancellable: false
            }, async () => {
                const request: CodeAnalysisRequest = {
                    code: selectedText,
                    language: language,
                    analysisType: 'explain'
                };

                const response = await this.geminiClient.analyzeCode(request, session.sessionId);

                if (response.success && response.analysis) {
                    this.showExplanationPanel(selectedText, response.analysis, language);
                } else {
                    vscode.window.showErrorMessage(`Failed to explain code: ${response.error}`);
                }
            });
        } catch (error) {
            vscode.window.showErrorMessage(`Error: ${error}`);
        }
    }

    /**
     * Improve current file
     */
    async improveCurrentFile(): Promise<void> {
        if (!this.authService.isAuthenticated()) {
            vscode.window.showWarningMessage('Please login first.');
            return;
        }

        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found.');
            return;
        }

        const document = editor.document;
        const fullText = document.getText();
        const language = document.languageId;
        const session = this.authService.getCurrentSession();

        if (!session) {
            vscode.window.showWarningMessage('Session expired. Please login again.');
            return;
        }

        try {
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Improving code with Gemini...',
                cancellable: false
            }, async () => {
                const request: CodeAnalysisRequest = {
                    code: fullText,
                    language: language,
                    analysisType: 'improve'
                };

                const response = await this.geminiClient.analyzeCode(request, session.sessionId);

                if (response.success && response.analysis) {
                    // Extract improved code from response
                    const improvedCode = this.codeUtils.extractCodeFromResponse(response.analysis);

                    if (improvedCode) {
                        this.pendingChanges.set(document.uri.toString(), improvedCode);
                        this.showDiffView(fullText, improvedCode, document.fileName);
                    } else {
                        this.showImprovementSuggestions(response.analysis);
                    }
                } else {
                    vscode.window.showErrorMessage(`Failed to improve code: ${response.error}`);
                }
            });
        } catch (error) {
            vscode.window.showErrorMessage(`Error: ${error}`);
        }
    }

    /**
     * Show diff view
     */
    async showDiffView(original?: string, modified?: string, fileName?: string): Promise<void> {
        if (!original || !modified) {
            vscode.window.showWarningMessage('No changes to preview.');
            return;
        }

        if (this.diffPanel) {
            this.diffPanel.reveal();
            return;
        }

        this.diffPanel = vscode.window.createWebviewPanel(
            'geminiDiff',
            `Gemini Suggestions - ${fileName || 'Untitled'}`,
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(this.context.extensionUri, 'src', 'webviews'),
                    vscode.Uri.joinPath(this.context.extensionUri, 'media')
                ]
            }
        );

        this.diffPanel.webview.html = this.getDiffViewHtml(original, modified, fileName);
        this.setupDiffPanelMessageHandling();

        this.diffPanel.onDidDispose(() => {
            this.diffPanel = null;
        });
    }

    /**
     * Apply pending changes
     */
    async applyPendingChanges(): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found.');
            return;
        }

        const documentUri = editor.document.uri.toString();
        const pendingCode = this.pendingChanges.get(documentUri);

        if (!pendingCode) {
            vscode.window.showWarningMessage('No pending changes to apply.');
            return;
        }

        try {
            const edit = new vscode.WorkspaceEdit();
            const fullRange = new vscode.Range(
                editor.document.positionAt(0),
                editor.document.positionAt(editor.document.getText().length)
            );

            edit.replace(editor.document.uri, fullRange, pendingCode);

            const success = await vscode.workspace.applyEdit(edit);

            if (success) {
                this.pendingChanges.delete(documentUri);
                vscode.window.showInformationMessage('Changes applied successfully!');

                if (this.diffPanel) {
                    this.diffPanel.dispose();
                }
            } else {
                vscode.window.showErrorMessage('Failed to apply changes.');
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Error applying changes: ${error}`);
        }
    }

    /**
     * Schedule auto-suggest (debounced)
     */
    scheduleAutoSuggest(document: vscode.TextDocument): void {
        if (this.autoSuggestTimeout) {
            clearTimeout(this.autoSuggestTimeout);
        }

        this.autoSuggestTimeout = setTimeout(() => {
            this.performAutoSuggest(document);
        }, 2000); // 2 second delay
    }

    /**
     * Perform auto-suggest
     */
    private async performAutoSuggest(document: vscode.TextDocument): Promise<void> {
        if (!this.authService.isAuthenticated()) {
            return;
        }

        const config = vscode.workspace.getConfiguration('geminiAgent');
        const supportedLanguages = config.get('languageSupport', []) as string[];

        if (!supportedLanguages.includes(document.languageId)) {
            return;
        }

        // Auto-suggest logic here
        // This could analyze the current context and provide suggestions
    }

    /**
     * Handle active editor change
     */
    onActiveEditorChanged(editor: vscode.TextEditor): void {
        // Update context or perform actions when editor changes
        console.log(`Active editor changed: ${editor.document.fileName}`);
    }

    /**
     * Setup chat panel message handling
     */
    private setupChatPanelMessageHandling(): void {
        if (!this.chatPanel) return;

        this.chatPanel.webview.onDidReceiveMessage(async (message) => {
            switch (message.command) {
                case 'sendMessage':
                    await this.handleChatMessage(message.text);
                    break;
                case 'clearChat':
                    this.clearChatHistory();
                    break;
            }
        });
    }

    /**
     * Setup diff panel message handling
     */
    private setupDiffPanelMessageHandling(): void {
        if (!this.diffPanel) return;

        this.diffPanel.webview.onDidReceiveMessage(async (message) => {
            switch (message.command) {
                case 'applyChanges':
                    await this.applyPendingChanges();
                    break;
                case 'rejectChanges':
                    this.rejectPendingChanges();
                    break;
            }
        });
    }

    /**
     * Handle chat message
     */
    private async handleChatMessage(text: string): Promise<void> {
        const session = this.authService.getCurrentSession();
        if (!session) return;

        try {
            const request: ChatRequest = {
                message: text,
                context: this.getEditorContext()
            };

            const response = await this.geminiClient.sendChatMessage(request, session.sessionId);

            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    command: 'addMessage',
                    message: {
                        text: response.response || response.error,
                        isUser: false,
                        timestamp: new Date().toISOString()
                    }
                });
            }
        } catch (error) {
            console.error('Chat message error:', error);
        }
    }

    /**
     * Get current editor context
     */
    private getEditorContext(): string {
        const editor = vscode.window.activeTextEditor;
        if (!editor) return '';

        const document = editor.document;
        const selection = editor.selection;

        let context = `File: ${document.fileName}\nLanguage: ${document.languageId}\n`;

        if (!selection.isEmpty) {
            context += `Selected code:\n${document.getText(selection)}`;
        }

        return context;
    }

    /**
     * Show explanation panel
     */
    private showExplanationPanel(code: string, explanation: string, language: string): void {
        const panel = vscode.window.createWebviewPanel(
            'codeExplanation',
            'Code Explanation',
            vscode.ViewColumn.Beside,
            { enableScripts: true }
        );

        panel.webview.html = `
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body { font-family: var(--vscode-font-family); padding: 20px; }
                    .code { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0; }
                    .explanation { line-height: 1.6; }
                </style>
            </head>
            <body>
                <h2>Code Explanation</h2>
                <h3>Original Code (${language}):</h3>
                <div class="code"><pre>${code}</pre></div>
                <h3>Explanation:</h3>
                <div class="explanation">${explanation}</div>
            </body>
            </html>
        `;
    }

    /**
     * Show improvement suggestions
     */
    private showImprovementSuggestions(suggestions: string): void {
        const panel = vscode.window.createWebviewPanel(
            'improvementSuggestions',
            'Improvement Suggestions',
            vscode.ViewColumn.Beside,
            { enableScripts: true }
        );

        panel.webview.html = `
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body { font-family: var(--vscode-font-family); padding: 20px; }
                    .suggestions { line-height: 1.6; }
                </style>
            </head>
            <body>
                <h2>Improvement Suggestions</h2>
                <div class="suggestions">${suggestions}</div>
            </body>
            </html>
        `;
    }

    /**
     * Clear chat history
     */
    private clearChatHistory(): void {
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                command: 'clearMessages'
            });
        }
    }

    /**
     * Reject pending changes
     */
    private rejectPendingChanges(): void {
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            const documentUri = editor.document.uri.toString();
            this.pendingChanges.delete(documentUri);
        }

        if (this.diffPanel) {
            this.diffPanel.dispose();
        }

        vscode.window.showInformationMessage('Changes rejected.');
    }

    /**
     * Get chat panel HTML
     */
    private getChatPanelHtml(): string {
        const styleUri = this.chatPanel?.webview.asWebviewUri(
            vscode.Uri.joinPath(this.context.extensionUri, 'media', 'styles.css')
        );

        // Read the HTML template and replace placeholders
        const fs = require('fs');
        const path = require('path');

        try {
            const htmlPath = path.join(this.context.extensionPath, 'src', 'webviews', 'mainPanel.html');
            let html = fs.readFileSync(htmlPath, 'utf8');

            // Replace placeholders
            html = html.replace('{{STYLES_URI}}', styleUri?.toString() || '');

            return html;
        } catch (error) {
            console.error('Failed to load chat panel HTML:', error);
            return this.getFallbackChatHtml();
        }
    }

    /**
     * Get diff view HTML
     */
    private getDiffViewHtml(original: string, modified: string, fileName?: string): string {
        const styleUri = this.diffPanel?.webview.asWebviewUri(
            vscode.Uri.joinPath(this.context.extensionUri, 'media', 'styles.css')
        );

        const fs = require('fs');
        const path = require('path');

        try {
            const htmlPath = path.join(this.context.extensionPath, 'src', 'webviews', 'diffView.html');
            let html = fs.readFileSync(htmlPath, 'utf8');

            // Replace placeholders
            html = html.replace('{{STYLES_URI}}', styleUri?.toString() || '');

            return html;
        } catch (error) {
            console.error('Failed to load diff view HTML:', error);
            return this.getFallbackDiffHtml(original, modified, fileName);
        }
    }

    /**
     * Fallback chat HTML if file loading fails
     */
    private getFallbackChatHtml(): string {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Gemini Chat</title>
                <style>
                    body { font-family: var(--vscode-font-family); padding: 20px; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
                    .user { background: #e3f2fd; text-align: right; }
                    .assistant { background: #f5f5f5; }
                    .input-container { position: fixed; bottom: 0; left: 0; right: 0; padding: 20px; background: var(--vscode-editor-background); }
                    .input-row { display: flex; gap: 10px; }
                    input { flex: 1; padding: 10px; }
                    button { padding: 10px 20px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h2>🤖 Gemini AI Chat</h2>
                    <div id="messages"></div>
                </div>
                <div class="input-container">
                    <div class="input-row">
                        <input type="text" id="messageInput" placeholder="Ask Gemini...">
                        <button onclick="sendMessage()">Send</button>
                    </div>
                </div>
                <script>
                    const vscode = acquireVsCodeApi();
                    function sendMessage() {
                        const input = document.getElementById('messageInput');
                        const text = input.value.trim();
                        if (text) {
                            vscode.postMessage({ command: 'sendMessage', text: text });
                            input.value = '';
                        }
                    }
                    document.getElementById('messageInput').addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') sendMessage();
                    });
                </script>
            </body>
            </html>
        `;
    }

    /**
     * Fallback diff HTML if file loading fails
     */
    private getFallbackDiffHtml(original: string, modified: string, fileName?: string): string {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Code Diff</title>
                <style>
                    body { font-family: var(--vscode-font-family); padding: 20px; }
                    .header { display: flex; justify-content: space-between; margin-bottom: 20px; }
                    .diff-container { display: flex; gap: 20px; }
                    .diff-side { flex: 1; }
                    .diff-header { background: #f5f5f5; padding: 10px; font-weight: bold; }
                    .code { background: #fafafa; padding: 15px; white-space: pre-wrap; font-family: monospace; }
                    button { padding: 8px 16px; margin: 5px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>Code Diff: ${fileName || 'Untitled'}</h2>
                    <div>
                        <button onclick="applyChanges()">Apply</button>
                        <button onclick="rejectChanges()">Reject</button>
                    </div>
                </div>
                <div class="diff-container">
                    <div class="diff-side">
                        <div class="diff-header">Original</div>
                        <div class="code">${original}</div>
                    </div>
                    <div class="diff-side">
                        <div class="diff-header">Modified</div>
                        <div class="code">${modified}</div>
                    </div>
                </div>
                <script>
                    const vscode = acquireVsCodeApi();
                    function applyChanges() {
                        vscode.postMessage({ command: 'applyChanges' });
                    }
                    function rejectChanges() {
                        vscode.postMessage({ command: 'rejectChanges' });
                    }
                </script>
            </body>
            </html>
        `;
    }

    /**
     * Dispose resources
     */
    dispose(): void {
        if (this.chatPanel) {
            this.chatPanel.dispose();
        }

        if (this.diffPanel) {
            this.diffPanel.dispose();
        }

        if (this.autoSuggestTimeout) {
            clearTimeout(this.autoSuggestTimeout);
        }
    }
}
