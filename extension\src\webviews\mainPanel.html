<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini AI Chat</title>
    <link href="{{STYLES_URI}}" rel="stylesheet">
    <style>
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: var(--vscode-editor-background);
        }

        .chat-header {
            background: var(--vscode-titleBar-activeBackground);
            color: var(--vscode-titleBar-activeForeground);
            padding: 15px 20px;
            border-bottom: 1px solid var(--vscode-panel-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-shrink: 0;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .gemini-icon {
            font-size: 1.5em;
        }

        .header-title {
            font-size: 1.2em;
            font-weight: 600;
            margin: 0;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
        }

        .user-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 1px solid var(--vscode-panel-border);
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            background: transparent;
            border: 1px solid var(--vscode-button-border);
            color: var(--vscode-button-foreground);
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85em;
            transition: background-color 0.2s;
        }

        .action-btn:hover {
            background: var(--vscode-button-hoverBackground);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: var(--vscode-editor-background);
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 12px;
            animation: fadeIn 0.3s ease-in;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
        }

        .message.user .message-avatar {
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
        }

        .message.assistant .message-avatar {
            background: linear-gradient(45deg, #4285f4, #34a853);
            color: white;
        }

        .message-content {
            max-width: 75%;
            padding: 12px 16px;
            border-radius: 12px;
            position: relative;
        }

        .message.user .message-content {
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: var(--vscode-editor-selectionBackground);
            border: 1px solid var(--vscode-panel-border);
            border-bottom-left-radius: 4px;
        }

        .message-text {
            line-height: 1.5;
            word-wrap: break-word;
        }

        .message-time {
            font-size: 0.75em;
            color: var(--vscode-descriptionForeground);
            margin-top: 5px;
            opacity: 0.7;
        }

        .message-actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .message-action-btn {
            background: transparent;
            border: none;
            color: var(--vscode-descriptionForeground);
            cursor: pointer;
            font-size: 0.8em;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .message-action-btn:hover {
            background: var(--vscode-list-hoverBackground);
        }

        .code-block {
            background: var(--vscode-textCodeBlock-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
            font-family: var(--vscode-editor-font-family);
            font-size: 0.9em;
            overflow-x: auto;
            position: relative;
        }

        .code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.8em;
            color: var(--vscode-descriptionForeground);
        }

        .copy-btn {
            background: var(--vscode-button-secondaryBackground);
            border: none;
            color: var(--vscode-button-secondaryForeground);
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.75em;
        }

        .chat-input-container {
            padding: 20px;
            border-top: 1px solid var(--vscode-panel-border);
            background: var(--vscode-editor-background);
            flex-shrink: 0;
        }

        .chat-input-row {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            padding: 12px 16px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 20px;
            background: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            resize: none;
            font-family: inherit;
            font-size: inherit;
            outline: none;
            transition: border-color 0.2s;
        }

        .chat-input:focus {
            border-color: var(--vscode-focusBorder);
        }

        .chat-input::placeholder {
            color: var(--vscode-input-placeholderForeground);
        }

        .send-button {
            width: 44px;
            height: 44px;
            border: none;
            border-radius: 50%;
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
            font-size: 1.1em;
        }

        .send-button:hover:not(:disabled) {
            background: var(--vscode-button-hoverBackground);
        }

        .send-button:disabled {
            background: var(--vscode-button-secondaryBackground);
            cursor: not-allowed;
            opacity: 0.6;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            color: var(--vscode-descriptionForeground);
            font-style: italic;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: var(--vscode-descriptionForeground);
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            color: var(--vscode-descriptionForeground);
        }

        .empty-icon {
            font-size: 3em;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 1.2em;
            margin-bottom: 8px;
            color: var(--vscode-foreground);
        }

        .empty-description {
            max-width: 300px;
            line-height: 1.5;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        /* Scrollbar styling */
        .chat-messages::-webkit-scrollbar {
            width: 8px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: var(--vscode-scrollbarSlider-background);
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: var(--vscode-scrollbarSlider-hoverBackground);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="header-left">
                <span class="gemini-icon">🤖</span>
                <h2 class="header-title">Gemini AI Assistant</h2>
            </div>
            <div class="header-right">
                <div class="user-info" id="userInfo">
                    <img class="user-avatar" id="userAvatar" src="" alt="User">
                    <span id="userName">Loading...</span>
                </div>
                <div class="action-buttons">
                    <button class="action-btn" onclick="clearChat()">Clear Chat</button>
                    <button class="action-btn" onclick="exportChat()">Export</button>
                </div>
            </div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="empty-state" id="emptyState">
                <div class="empty-icon">💬</div>
                <div class="empty-title">Start a conversation</div>
                <div class="empty-description">
                    Ask me anything about your code, request explanations, or get suggestions for improvements.
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
            <span>Gemini is thinking...</span>
        </div>

        <div class="chat-input-container">
            <div class="chat-input-row">
                <textarea 
                    class="chat-input" 
                    id="chatInput" 
                    placeholder="Ask Gemini about your code..."
                    rows="1"
                ></textarea>
                <button class="send-button" id="sendButton" onclick="sendMessage()">
                    ➤
                </button>
            </div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let messages = [];

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            setupEventListeners();
            loadUserInfo();
            loadChatHistory();
        });

        function setupEventListeners() {
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');

            // Auto-resize textarea
            chatInput.addEventListener('input', () => {
                chatInput.style.height = 'auto';
                chatInput.style.height = Math.min(chatInput.scrollHeight, 120) + 'px';
                
                // Enable/disable send button
                sendButton.disabled = !chatInput.value.trim();
            });

            // Send on Enter (but not Shift+Enter)
            chatInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        function loadUserInfo() {
            // Request user info from extension
            vscode.postMessage({ command: 'getUserInfo' });
        }

        function loadChatHistory() {
            // Request chat history from extension
            vscode.postMessage({ command: 'getChatHistory' });
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const text = input.value.trim();
            
            if (!text) return;

            // Add user message
            addMessage({
                text: text,
                isUser: true,
                timestamp: new Date().toISOString()
            });

            // Clear input
            input.value = '';
            input.style.height = 'auto';
            document.getElementById('sendButton').disabled = true;

            // Show typing indicator
            showTypingIndicator();

            // Send to extension
            vscode.postMessage({
                command: 'sendMessage',
                text: text
            });
        }

        function addMessage(message) {
            const messagesContainer = document.getElementById('chatMessages');
            const emptyState = document.getElementById('emptyState');
            
            // Hide empty state
            if (emptyState) {
                emptyState.style.display = 'none';
            }

            // Create message element
            const messageEl = document.createElement('div');
            messageEl.className = `message ${message.isUser ? 'user' : 'assistant'}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = message.isUser ? '👤' : '🤖';
            
            const content = document.createElement('div');
            content.className = 'message-content';
            
            const text = document.createElement('div');
            text.className = 'message-text';
            text.innerHTML = formatMessageText(message.text);
            
            const time = document.createElement('div');
            time.className = 'message-time';
            time.textContent = formatTime(message.timestamp);
            
            content.appendChild(text);
            content.appendChild(time);
            
            // Add actions for assistant messages
            if (!message.isUser) {
                const actions = document.createElement('div');
                actions.className = 'message-actions';
                actions.innerHTML = `
                    <button class="message-action-btn" onclick="copyMessage(this)">Copy</button>
                    <button class="message-action-btn" onclick="applyCode(this)">Apply Code</button>
                `;
                content.appendChild(actions);
            }
            
            messageEl.appendChild(avatar);
            messageEl.appendChild(content);
            
            messagesContainer.appendChild(messageEl);
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            // Store message
            messages.push(message);
        }

        function formatMessageText(text) {
            // Convert markdown-style code blocks
            text = text.replace(/```(\w+)?\n([\s\S]*?)\n```/g, (match, lang, code) => {
                return `
                    <div class="code-block">
                        <div class="code-header">
                            <span>${lang || 'code'}</span>
                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                        </div>
                        <pre><code>${escapeHtml(code)}</code></pre>
                    </div>
                `;
            });
            
            // Convert inline code
            text = text.replace(/`([^`]+)`/g, '<code>$1</code>');
            
            // Convert line breaks
            text = text.replace(/\n/g, '<br>');
            
            return text;
        }

        function formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'flex';
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        function clearChat() {
            if (confirm('Are you sure you want to clear the chat history?')) {
                messages = [];
                document.getElementById('chatMessages').innerHTML = `
                    <div class="empty-state" id="emptyState">
                        <div class="empty-icon">💬</div>
                        <div class="empty-title">Start a conversation</div>
                        <div class="empty-description">
                            Ask me anything about your code, request explanations, or get suggestions for improvements.
                        </div>
                    </div>
                `;
                vscode.postMessage({ command: 'clearChat' });
            }
        }

        function exportChat() {
            vscode.postMessage({ 
                command: 'exportChat',
                messages: messages
            });
        }

        function copyMessage(button) {
            const messageText = button.closest('.message-content').querySelector('.message-text').textContent;
            navigator.clipboard.writeText(messageText);
            button.textContent = 'Copied!';
            setTimeout(() => button.textContent = 'Copy', 2000);
        }

        function copyCode(button) {
            const codeBlock = button.closest('.code-block').querySelector('code');
            navigator.clipboard.writeText(codeBlock.textContent);
            button.textContent = 'Copied!';
            setTimeout(() => button.textContent = 'Copy', 2000);
        }

        function applyCode(button) {
            const messageContent = button.closest('.message-content');
            const codeBlocks = messageContent.querySelectorAll('.code-block code');
            
            if (codeBlocks.length > 0) {
                const code = Array.from(codeBlocks).map(block => block.textContent).join('\n\n');
                vscode.postMessage({
                    command: 'applyCode',
                    code: code
                });
            }
        }

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.command) {
                case 'addMessage':
                    hideTypingIndicator();
                    addMessage(message.message);
                    break;
                case 'clearMessages':
                    clearChat();
                    break;
                case 'setUserInfo':
                    document.getElementById('userName').textContent = message.user.name;
                    document.getElementById('userAvatar').src = message.user.picture;
                    break;
                case 'setChatHistory':
                    message.messages.forEach(msg => addMessage(msg));
                    break;
            }
        });
    </script>
</body>
</html>
