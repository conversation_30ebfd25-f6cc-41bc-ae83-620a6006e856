# دليل المطور - Gemini VSCode Agent

## 🏗️ هيكل المشروع

```
gemini-vscode-agent/
├── extension/                   # إضافة VS Code
│   ├── src/
│   │   ├── extension.ts        # نقطة الدخول الرئيسية
│   │   ├── agent/              # Agent الذكي
│   │   │   └── GeminiAgent.ts
│   │   ├── services/           # خدمات الاتصال
│   │   │   ├── googleAuth.ts   # تسجيل الدخول Gmail
│   │   │   ├── geminiClient.ts # عميل Gemini API
│   │   │   └── codeUtils.ts    # أدوات تحليل الكود
│   │   ├── webviews/           # واجهات المستخدم
│   │   │   ├── login.html
│   │   │   ├── mainPanel.html
│   │   │   └── diffView.html
│   │   └── test/               # الاختبارات
│   ├── media/                  # الأصول والتصميم
│   │   └── styles.css
│   ├── package.json
│   └── tsconfig.json
│
├── proxy-server/               # الخادم الداخلي
│   ├── index.js               # خادم Express الرئيسي
│   ├── start.js               # سكريبت التشغيل
│   ├── test.js                # اختبارات الخادم
│   ├── routes/                # مسارات API
│   │   ├── auth.js            # مسارات التحقق
│   │   └── gemini.js          # مسارات Gemini
│   ├── auth/                  # خدمات التحقق
│   │   └── verifyGoogleToken.js
│   ├── .env.example           # مثال متغيرات البيئة
│   └── package.json
│
├── .vscode/                   # إعدادات VS Code
│   ├── launch.json           # تكوين التشغيل
│   ├── tasks.json            # المهام
│   ├── settings.json         # الإعدادات
│   └── extensions.json       # توصيات الإضافات
│
├── start.bat                 # تشغيل سريع (Windows)
├── start.sh                  # تشغيل سريع (Linux/Mac)
├── README.md                 # الوثائق الرئيسية
├── INSTALLATION.md           # تعليمات التثبيت
└── DEVELOPER.md              # هذا الملف
```

## 🛠️ إعداد بيئة التطوير

### 1. المتطلبات
- Node.js 16.0.0+
- VS Code 1.74.0+
- Git

### 2. استنساخ المشروع
```bash
git clone <repository-url>
cd gemini-vscode-agent
```

### 3. تثبيت المكتبات
```bash
# تثبيت مكتبات الإضافة
cd extension
npm install

# تثبيت مكتبات الخادم
cd ../proxy-server
npm install
```

### 4. إعداد متغيرات البيئة
```bash
cd proxy-server
cp .env.example .env
# تحرير .env وإضافة المفاتيح المطلوبة
```

## 🚀 التطوير

### تشغيل الخادم
```bash
cd proxy-server
npm run dev  # للتطوير مع إعادة التشغيل التلقائي
npm start    # للتشغيل العادي
```

### تطوير الإضافة
1. افتح VS Code في مجلد المشروع
2. اضغط `F5` لتشغيل Extension Development Host
3. أو استخدم "Run Extension" من Debug panel

### مراقبة التغييرات
```bash
cd extension
npm run watch  # مراقبة تغييرات TypeScript
```

## 🧪 الاختبارات

### اختبار الخادم
```bash
cd proxy-server
npm test
```

### اختبار الإضافة
```bash
cd extension
npm test
```

### اختبار شامل
استخدم VS Code Task: "Test All"

## 📝 معمارية النظام

### تدفق البيانات
```
VS Code Extension → Proxy Server → Google Gemini API
                ↓
            WebView UI ← Response Processing
```

### مكونات النظام

#### 1. Extension (extension/src/)
- **extension.ts**: نقطة الدخول، تسجيل الأوامر
- **GeminiAgent.ts**: المنطق الرئيسي للـ AI Agent
- **googleAuth.ts**: إدارة تسجيل الدخول Gmail
- **geminiClient.ts**: التواصل مع الخادم الداخلي
- **codeUtils.ts**: أدوات تحليل ومعالجة الكود

#### 2. Proxy Server (proxy-server/)
- **index.js**: خادم Express الرئيسي
- **auth/**: التحقق من Google Tokens
- **routes/**: مسارات API للمصادقة و Gemini

#### 3. WebViews (extension/src/webviews/)
- **login.html**: واجهة تسجيل الدخول
- **mainPanel.html**: لوحة المحادثة الرئيسية
- **diffView.html**: عرض الفروقات والتعديلات

## 🔧 APIs والواجهات

### Gemini Client API
```typescript
interface GeminiResponse {
    success: boolean;
    response?: string;
    analysis?: string;
    generatedCode?: string;
    error?: string;
}

interface CodeAnalysisRequest {
    code: string;
    language?: string;
    analysisType: 'explain' | 'improve' | 'debug' | 'refactor';
}
```

### Auth Service API
```typescript
interface AuthSession {
    sessionId: string;
    user: UserInfo;
    expiresAt: Date;
}

interface UserInfo {
    id: string;
    email: string;
    name: string;
    picture: string;
}
```

## 🎨 تطوير الواجهات

### WebView Communication
```javascript
// من WebView إلى Extension
vscode.postMessage({
    command: 'sendMessage',
    text: 'Hello Gemini'
});

// من Extension إلى WebView
panel.webview.postMessage({
    command: 'addMessage',
    message: { text: 'Response', isUser: false }
});
```

### CSS Variables
استخدم متغيرات VS Code CSS:
```css
color: var(--vscode-foreground);
background: var(--vscode-editor-background);
border: 1px solid var(--vscode-panel-border);
```

## 🔍 التصحيح (Debugging)

### تصحيح الإضافة
1. ضع breakpoints في TypeScript
2. استخدم "Run Extension" configuration
3. افتح Developer Tools في Extension Host

### تصحيح الخادم
1. استخدم "Debug Proxy Server" configuration
2. أو استخدم `console.log` في الكود
3. راقب logs في terminal

### تصحيح WebViews
1. افتح Developer Tools في VS Code
2. اذهب إلى Console tab
3. ابحث عن WebView logs

## 📊 مراقبة الأداء

### Metrics مهمة
- وقت استجابة Gemini API
- معدل نجاح المصادقة
- استخدام الذاكرة
- أخطاء الشبكة

### Logging
```javascript
// في الخادم
console.log(`🤖 Gemini request from ${user.email}`);

// في الإضافة
console.log('✅ Code analysis completed');
```

## 🚢 النشر

### بناء الإضافة
```bash
cd extension
npm run compile
vsce package  # إنشاء .vsix file
```

### نشر الخادم
1. إعداد متغيرات البيئة للإنتاج
2. استخدام PM2 أو Docker
3. إعداد HTTPS و SSL

## 🔒 الأمان

### Best Practices
- لا تضع API Keys في الكود
- استخدم HTTPS في الإنتاج
- تحقق من صحة جميع المدخلات
- استخدم rate limiting
- راقب الوصول غير المصرح به

### Environment Variables
```env
# مطلوبة
GOOGLE_API_KEY=your_key
GOOGLE_CLIENT_ID=your_id

# اختيارية
NODE_ENV=production
PORT=5000
RATE_LIMIT_MAX_REQUESTS=100
```

## 🤝 المساهمة

### قواعد الكود
- استخدم TypeScript للإضافة
- اتبع ESLint rules
- اكتب اختبارات للوظائف الجديدة
- استخدم تعليقات واضحة

### Git Workflow
1. إنشاء branch جديد للميزة
2. تطوير وتجربة التغييرات
3. تشغيل الاختبارات
4. إنشاء Pull Request

### Testing Checklist
- [ ] اختبار تسجيل الدخول Gmail
- [ ] اختبار تحليل الكود
- [ ] اختبار عرض الفروقات
- [ ] اختبار المحادثة
- [ ] اختبار معالجة الأخطاء

## 📚 مراجع مفيدة

- [VS Code Extension API](https://code.visualstudio.com/api)
- [Google Gemini API](https://ai.google.dev/docs)
- [Google OAuth 2.0](https://developers.google.com/identity/protocols/oauth2)
- [Express.js Documentation](https://expressjs.com/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

## ❓ الأسئلة الشائعة

**Q: كيف أضيف لغة برمجة جديدة؟**
A: أضف اللغة في `languageSupport` في package.json وحدث `codeUtils.ts`

**Q: كيف أغير تصميم الواجهات؟**
A: حرر ملفات HTML في `webviews/` و CSS في `media/styles.css`

**Q: كيف أضيف ميزة جديدة؟**
A: أضف command في package.json، نفذه في extension.ts، واختبره

---

للمزيد من المساعدة، راجع الوثائق أو أنشئ issue في المشروع.
