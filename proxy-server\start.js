#!/usr/bin/env node

/**
 * Gemini VSCode Agent Proxy Server Startup Script
 * This script handles the startup and configuration of the proxy server
 */

const fs = require('fs');
const path = require('path');

// Check if .env file exists
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
    console.log('🔧 Setting up environment configuration...');
    
    // Copy .env.example to .env
    const examplePath = path.join(__dirname, '.env.example');
    if (fs.existsSync(examplePath)) {
        fs.copyFileSync(examplePath, envPath);
        console.log('✅ Created .env file from template');
        console.log('⚠️  Please edit .env file and add your Google API credentials');
        console.log('');
        console.log('Required environment variables:');
        console.log('- GOOGLE_API_KEY: Your Google Gemini API key');
        console.log('- GOOGLE_CLIENT_ID: Your Google OAuth Client ID');
        console.log('- GOOGLE_CLIENT_SECRET: Your Google OAuth Client Secret');
        console.log('');
        console.log('Get these from: https://console.cloud.google.com/');
        console.log('');
        process.exit(1);
    } else {
        console.error('❌ .env.example file not found');
        process.exit(1);
    }
}

// Load environment variables
require('dotenv').config();

// Validate required environment variables
const requiredVars = [
    'GOOGLE_API_KEY',
    'GOOGLE_CLIENT_ID'
];

const missingVars = requiredVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingVars.forEach(varName => {
        console.error(`   - ${varName}`);
    });
    console.log('');
    console.log('Please edit the .env file and add the missing variables.');
    console.log('Get your Google API credentials from: https://console.cloud.google.com/');
    process.exit(1);
}

// Check if dependencies are installed
const packageJsonPath = path.join(__dirname, 'package.json');
const nodeModulesPath = path.join(__dirname, 'node_modules');

if (!fs.existsSync(nodeModulesPath)) {
    console.log('📦 Installing dependencies...');
    const { execSync } = require('child_process');
    
    try {
        execSync('npm install', { 
            cwd: __dirname, 
            stdio: 'inherit' 
        });
        console.log('✅ Dependencies installed successfully');
    } catch (error) {
        console.error('❌ Failed to install dependencies:', error.message);
        process.exit(1);
    }
}

// Start the server
console.log('🚀 Starting Gemini VSCode Agent Proxy Server...');
console.log('');

// Import and start the main server
try {
    require('./index.js');
} catch (error) {
    console.error('❌ Failed to start server:', error.message);
    console.error(error.stack);
    process.exit(1);
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT, shutting down gracefully...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
    process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('💥 Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
