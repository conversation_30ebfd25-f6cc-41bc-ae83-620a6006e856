{"name": "gemini-vscode-agent", "displayName": "Gemini VSCode Agent", "description": "AI-powered coding assistant using Google Gemini with Gmail authentication", "version": "1.0.0", "publisher": "gemini-agent", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Education"], "keywords": ["ai", "gemini", "google", "assistant", "code-analysis"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "geminiAgent.loginGmail", "title": "Login with G<PERSON>", "category": "Gemini"}, {"command": "geminiAgent.chat", "title": "Open Chat Panel", "category": "Gemini", "icon": "$(comment-discussion)"}, {"command": "geminiAgent.explainSelection", "title": "Explain Selected Code", "category": "Gemini"}, {"command": "geminiAgent.improveCode", "title": "Improve Current File", "category": "Gemini"}, {"command": "geminiAgent.showDiff", "title": "Preview Suggested Changes", "category": "Gemini"}, {"command": "geminiAgent.applyChanges", "title": "Apply Suggested Changes", "category": "Gemini"}], "menus": {"editor/context": [{"command": "geminiAgent.explainSelection", "when": "editorHasSelection", "group": "gemini"}, {"command": "geminiAgent.improveCode", "group": "gemini"}], "commandPalette": [{"command": "geminiAgent.loginGmail"}, {"command": "geminiAgent.chat"}, {"command": "geminiAgent.explainSelection"}, {"command": "geminiAgent.improveCode"}, {"command": "geminiAgent.showDiff"}, {"command": "geminiAgent.applyChanges"}], "editor/title": [{"command": "geminiAgent.chat", "when": "resourceExtname", "group": "navigation"}]}, "configuration": {"title": "Gemini Agent", "properties": {"geminiAgent.autoApply": {"type": "boolean", "default": false, "description": "Automatically apply suggested changes without preview"}, "geminiAgent.languageSupport": {"type": "array", "default": ["javascript", "typescript", "python", "php", "dart", "java", "csharp"], "description": "Supported programming languages"}, "geminiAgent.proxyUrl": {"type": "string", "default": "http://localhost:5000", "description": "Proxy server URL"}, "geminiAgent.theme": {"type": "string", "enum": ["light", "dark", "auto"], "default": "auto", "description": "UI theme preference"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@types/mocha": "^10.0.1", "@types/glob": "^8.0.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "@vscode/test-electron": "^2.2.0", "eslint": "^8.28.0", "glob": "^8.0.3", "mocha": "^10.1.0", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.2"}}