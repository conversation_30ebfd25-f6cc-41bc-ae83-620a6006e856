import * as vscode from 'vscode';

export class CodeUtils {
    /**
     * Extract code blocks from Gemini response
     */
    extractCodeFromResponse(response: string): string | null {
        // Look for code blocks in markdown format
        const codeBlockRegex = /```[\w]*\n([\s\S]*?)\n```/g;
        const matches = response.match(codeBlockRegex);
        
        if (matches && matches.length > 0) {
            // Return the first code block, removing the markdown syntax
            return matches[0].replace(/```[\w]*\n/, '').replace(/\n```$/, '');
        }
        
        // If no code blocks found, try to extract code-like content
        const lines = response.split('\n');
        const codeLines: string[] = [];
        let inCodeSection = false;
        
        for (const line of lines) {
            // Detect if this looks like code
            if (this.looksLikeCode(line)) {
                inCodeSection = true;
                codeLines.push(line);
            } else if (inCodeSection && line.trim() === '') {
                codeLines.push(line); // Keep empty lines in code
            } else if (inCodeSection) {
                break; // End of code section
            }
        }
        
        return codeLines.length > 0 ? codeLines.join('\n') : null;
    }

    /**
     * Check if a line looks like code
     */
    private looksLikeCode(line: string): boolean {
        const trimmed = line.trim();
        
        // Common code patterns
        const codePatterns = [
            /^(function|const|let|var|class|interface|type)\s+/,
            /^(public|private|protected|static)\s+/,
            /^(if|else|for|while|switch|case|try|catch)\s*\(/,
            /^(import|export|from)\s+/,
            /^\s*\/\/|^\s*\/\*|^\s*\*/,
            /^\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*[=:]/,
            /^\s*[{}();]/,
            /^\s*return\s+/,
            /^\s*\w+\.\w+/
        ];
        
        return codePatterns.some(pattern => pattern.test(trimmed));
    }

    /**
     * Get language-specific formatting
     */
    formatCode(code: string, language: string): string {
        // Basic formatting based on language
        switch (language.toLowerCase()) {
            case 'javascript':
            case 'typescript':
                return this.formatJavaScript(code);
            case 'python':
                return this.formatPython(code);
            case 'java':
                return this.formatJava(code);
            default:
                return code;
        }
    }

    /**
     * Format JavaScript/TypeScript code
     */
    private formatJavaScript(code: string): string {
        // Basic indentation and formatting
        const lines = code.split('\n');
        let indentLevel = 0;
        const indentSize = 2;
        
        return lines.map(line => {
            const trimmed = line.trim();
            
            if (trimmed.includes('}')) {
                indentLevel = Math.max(0, indentLevel - 1);
            }
            
            const formatted = ' '.repeat(indentLevel * indentSize) + trimmed;
            
            if (trimmed.includes('{')) {
                indentLevel++;
            }
            
            return formatted;
        }).join('\n');
    }

    /**
     * Format Python code
     */
    private formatPython(code: string): string {
        // Basic Python formatting
        const lines = code.split('\n');
        let indentLevel = 0;
        const indentSize = 4;
        
        return lines.map(line => {
            const trimmed = line.trim();
            
            if (trimmed.startsWith('except') || trimmed.startsWith('elif') || 
                trimmed.startsWith('else') || trimmed.startsWith('finally')) {
                indentLevel = Math.max(0, indentLevel - 1);
            }
            
            const formatted = ' '.repeat(indentLevel * indentSize) + trimmed;
            
            if (trimmed.endsWith(':')) {
                indentLevel++;
            }
            
            return formatted;
        }).join('\n');
    }

    /**
     * Format Java code
     */
    private formatJava(code: string): string {
        // Basic Java formatting
        return this.formatJavaScript(code); // Similar to JavaScript
    }

    /**
     * Analyze code complexity
     */
    analyzeComplexity(code: string): {
        lines: number;
        functions: number;
        classes: number;
        complexity: 'low' | 'medium' | 'high';
    } {
        const lines = code.split('\n').filter(line => line.trim().length > 0).length;
        
        const functionMatches = code.match(/function\s+\w+|=>\s*{|\w+\s*\(/g) || [];
        const functions = functionMatches.length;
        
        const classMatches = code.match(/class\s+\w+/g) || [];
        const classes = classMatches.length;
        
        let complexity: 'low' | 'medium' | 'high' = 'low';
        if (lines > 100 || functions > 10 || classes > 3) {
            complexity = 'high';
        } else if (lines > 50 || functions > 5 || classes > 1) {
            complexity = 'medium';
        }
        
        return { lines, functions, classes, complexity };
    }

    /**
     * Generate code documentation
     */
    generateDocumentation(code: string, language: string): string {
        const analysis = this.analyzeComplexity(code);
        
        let doc = `# Code Documentation\n\n`;
        doc += `**Language:** ${language}\n`;
        doc += `**Lines of Code:** ${analysis.lines}\n`;
        doc += `**Functions:** ${analysis.functions}\n`;
        doc += `**Classes:** ${analysis.classes}\n`;
        doc += `**Complexity:** ${analysis.complexity}\n\n`;
        
        // Extract function signatures
        const functions = this.extractFunctions(code, language);
        if (functions.length > 0) {
            doc += `## Functions\n\n`;
            functions.forEach(func => {
                doc += `- \`${func}\`\n`;
            });
            doc += '\n';
        }
        
        return doc;
    }

    /**
     * Extract function signatures from code
     */
    private extractFunctions(code: string, language: string): string[] {
        const functions: string[] = [];
        
        switch (language.toLowerCase()) {
            case 'javascript':
            case 'typescript':
                const jsMatches = code.match(/function\s+\w+\([^)]*\)|const\s+\w+\s*=\s*\([^)]*\)\s*=>/g);
                if (jsMatches) functions.push(...jsMatches);
                break;
                
            case 'python':
                const pyMatches = code.match(/def\s+\w+\([^)]*\):/g);
                if (pyMatches) functions.push(...pyMatches);
                break;
                
            case 'java':
                const javaMatches = code.match(/(public|private|protected)?\s*(static)?\s*\w+\s+\w+\([^)]*\)/g);
                if (javaMatches) functions.push(...javaMatches);
                break;
        }
        
        return functions;
    }

    /**
     * Validate code syntax (basic)
     */
    validateSyntax(code: string, language: string): {
        isValid: boolean;
        errors: string[];
    } {
        const errors: string[] = [];
        
        // Basic syntax validation
        const lines = code.split('\n');
        let braceCount = 0;
        let parenCount = 0;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            // Count braces and parentheses
            for (const char of line) {
                if (char === '{') braceCount++;
                if (char === '}') braceCount--;
                if (char === '(') parenCount++;
                if (char === ')') parenCount--;
            }
        }
        
        if (braceCount !== 0) {
            errors.push('Mismatched braces');
        }
        
        if (parenCount !== 0) {
            errors.push('Mismatched parentheses');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Get code suggestions based on context
     */
    getCodeSuggestions(code: string, cursorPosition: number, language: string): string[] {
        const suggestions: string[] = [];
        
        // Get the current line and context
        const beforeCursor = code.substring(0, cursorPosition);
        const lines = beforeCursor.split('\n');
        const currentLine = lines[lines.length - 1];
        
        // Language-specific suggestions
        switch (language.toLowerCase()) {
            case 'javascript':
            case 'typescript':
                if (currentLine.includes('console.')) {
                    suggestions.push('log()', 'error()', 'warn()', 'info()');
                }
                if (currentLine.includes('document.')) {
                    suggestions.push('getElementById()', 'querySelector()', 'createElement()');
                }
                break;
                
            case 'python':
                if (currentLine.includes('print(')) {
                    suggestions.push('f"string"', '"string"', 'variable');
                }
                break;
        }
        
        return suggestions;
    }
}
