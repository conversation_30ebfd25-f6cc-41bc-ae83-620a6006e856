import * as vscode from 'vscode';
import { GeminiAgent } from './agent/GeminiAgent';
import { GoogleAuthService } from './services/googleAuth';
import { GeminiClient } from './services/geminiClient';

let geminiAgent: GeminiAgent;
let authService: GoogleAuthService;
let geminiClient: GeminiClient;

export function activate(context: vscode.ExtensionContext) {
    console.log('🚀 Gemini VSCode Agent is now active!');

    // Initialize services
    authService = new GoogleAuthService(context);
    geminiClient = new GeminiClient();
    geminiAgent = new GeminiAgent(context, authService, geminiClient);

    // Register commands
    registerCommands(context);

    // Show welcome message on first activation
    showWelcomeMessage(context);
}

function registerCommands(context: vscode.ExtensionContext) {
    // Login with Gmail command
    const loginCommand = vscode.commands.registerCommand('geminiAgent.loginGmail', async () => {
        try {
            await geminiAgent.loginWithGmail();
        } catch (error) {
            vscode.window.showErrorMessage(`Login failed: ${error}`);
        }
    });

    // Open chat panel command
    const chatCommand = vscode.commands.registerCommand('geminiAgent.chat', async () => {
        try {
            await geminiAgent.openChatPanel();
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to open chat: ${error}`);
        }
    });

    // Explain selected code command
    const explainCommand = vscode.commands.registerCommand('geminiAgent.explainSelection', async () => {
        try {
            await geminiAgent.explainSelectedCode();
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to explain code: ${error}`);
        }
    });

    // Improve current file command
    const improveCommand = vscode.commands.registerCommand('geminiAgent.improveCode', async () => {
        try {
            await geminiAgent.improveCurrentFile();
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to improve code: ${error}`);
        }
    });

    // Show diff command
    const diffCommand = vscode.commands.registerCommand('geminiAgent.showDiff', async () => {
        try {
            await geminiAgent.showDiffView();
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to show diff: ${error}`);
        }
    });

    // Apply changes command
    const applyCommand = vscode.commands.registerCommand('geminiAgent.applyChanges', async () => {
        try {
            await geminiAgent.applyPendingChanges();
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to apply changes: ${error}`);
        }
    });

    // Register all commands
    context.subscriptions.push(
        loginCommand,
        chatCommand,
        explainCommand,
        improveCommand,
        diffCommand,
        applyCommand
    );

    // Register status bar item
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.text = "$(robot) Gemini";
    statusBarItem.tooltip = "Gemini AI Assistant";
    statusBarItem.command = 'geminiAgent.chat';
    statusBarItem.show();
    context.subscriptions.push(statusBarItem);

    console.log('✅ All commands registered successfully');
}

function showWelcomeMessage(context: vscode.ExtensionContext) {
    const hasShownWelcome = context.globalState.get('gemini.hasShownWelcome', false);
    
    if (!hasShownWelcome) {
        vscode.window.showInformationMessage(
            'Welcome to Gemini VSCode Agent! Click "Login with Gmail" to get started.',
            'Login with Gmail',
            'Learn More'
        ).then(selection => {
            if (selection === 'Login with Gmail') {
                vscode.commands.executeCommand('geminiAgent.loginGmail');
            } else if (selection === 'Learn More') {
                vscode.env.openExternal(vscode.Uri.parse('https://github.com/your-repo/gemini-vscode-agent'));
            }
        });

        context.globalState.update('gemini.hasShownWelcome', true);
    }
}

// Configuration change handler
vscode.workspace.onDidChangeConfiguration(event => {
    if (event.affectsConfiguration('geminiAgent')) {
        console.log('📝 Gemini Agent configuration changed');
        
        // Reload services if needed
        if (event.affectsConfiguration('geminiAgent.proxyUrl')) {
            geminiClient.updateProxyUrl();
        }
    }
});

// Document change handler for auto-suggestions
vscode.workspace.onDidChangeTextDocument(event => {
    // Only process if auto-suggestions are enabled
    const config = vscode.workspace.getConfiguration('geminiAgent');
    const autoSuggest = config.get('autoSuggest', false);
    
    if (autoSuggest && geminiAgent) {
        // Debounce the auto-suggest to avoid too many requests
        geminiAgent.scheduleAutoSuggest(event.document);
    }
});

// Window state change handler
vscode.window.onDidChangeActiveTextEditor(editor => {
    if (editor && geminiAgent) {
        geminiAgent.onActiveEditorChanged(editor);
    }
});

export function deactivate() {
    console.log('👋 Gemini VSCode Agent is being deactivated');
    
    // Clean up resources
    if (geminiAgent) {
        geminiAgent.dispose();
    }
    
    if (authService) {
        authService.dispose();
    }
    
    if (geminiClient) {
        geminiClient.dispose();
    }
}
