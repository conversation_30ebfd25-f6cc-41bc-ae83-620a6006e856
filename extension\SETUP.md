# إعداد Gemini VSCode Agent

## الخطوة 1: <PERSON>ن<PERSON><PERSON><PERSON> Google OAuth Client ID

لتتمكن من تسجيل الدخول باستخدام Gmail، تحتاج إلى إنشاء Google OAuth Client ID:

### 1. انتق<PERSON> إلى Google Cloud Console
- اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
- قم بتسجيل الدخول بحساب Google الخاص بك

### 2. إنشاء مشروع جديد (إذا لم يكن لديك مشروع)
- انقر على "Select a project" في الأعلى
- انقر على "New Project"
- أدخل اسم المشروع (مثل "Gemini VSCode Agent")
- انقر على "Create"

### 3. تفعيل Google+ API
- في القائمة الجانبية، انقر على "APIs & Services" > "Library"
- اب<PERSON><PERSON> عن "Google+ API"
- انقر عليه وانقر على "Enable"

### 4. إنشاء OAuth 2.0 Client ID
- في القائمة الجانبية، انقر على "APIs & Services" > "Credentials"
- انقر على "Create Credentials" > "OAuth client ID"
- إذا لم تكن قد أعددت OAuth consent screen، ستحتاج لإعداده أولاً:
  - انقر على "Configure Consent Screen"
  - اختر "External" وانقر على "Create"
  - املأ المعلومات المطلوبة:
    - App name: "Gemini VSCode Agent"
    - User support email: بريدك الإلكتروني
    - Developer contact information: بريدك الإلكتروني
  - انقر على "Save and Continue"
  - في صفحة "Scopes"، انقر على "Save and Continue"
  - في صفحة "Test users"، أضف بريدك الإلكتروني وانقر على "Save and Continue"

### 5. إنشاء Client ID
- ارجع إلى "Credentials" وانقر على "Create Credentials" > "OAuth client ID"
- اختر "Web application"
- أدخل اسم (مثل "VSCode Extension")
- في "Authorized JavaScript origins"، أضف:
  - `https://accounts.google.com`
- في "Authorized redirect URIs"، أضف:
  - `https://accounts.google.com/oauth/authorize`
- انقر على "Create"

### 6. نسخ Client ID
- ستظهر نافذة تحتوي على Client ID
- انسخ Client ID (يبدأ بشيء مثل: `*********-abcdefg.apps.googleusercontent.com`)

## الخطوة 2: إعداد VSCode Extension

### 1. فتح إعدادات VSCode
- اضغط `Ctrl+,` (أو `Cmd+,` على Mac)
- ابحث عن "geminiAgent.googleClientId"

### 2. إدخال Client ID
- الصق Client ID الذي نسخته في الحقل
- احفظ الإعدادات

## الخطوة 3: تشغيل Extension

### 1. تجميع Extension
```bash
cd extension
npm install
npm run compile
```

### 2. تشغيل Extension في Development Mode
- اضغط `F5` في VSCode لفتح نافذة Extension Development Host
- أو اذهب إلى Run > Start Debugging

### 3. تجربة تسجيل الدخول
- في النافذة الجديدة، اضغط `Ctrl+Shift+P`
- ابحث عن "Gemini: Login with Gmail"
- انقر عليه وجرب تسجيل الدخول

## استكشاف الأخطاء

### إذا لم يعمل زر Google:
1. تأكد من أن Client ID صحيح
2. تأكد من أن Google+ API مفعل
3. تأكد من أن OAuth consent screen معد بشكل صحيح
4. تحقق من Developer Console في المتصفح للأخطاء

### إذا ظهرت رسالة "Client ID not configured":
1. تأكد من إدخال Client ID في إعدادات VSCode
2. أعد تشغيل VSCode بعد إدخال Client ID

### للحصول على مساعدة إضافية:
- تحقق من Developer Console في المتصفح (F12)
- تحقق من Output panel في VSCode (View > Output > Gemini Agent)
