<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login with Gmail - Gemini VSCode Agent</title>
    <link href="{{STYLES_URI}}" rel="stylesheet">
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <style>
        .login-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .login-card {
            background: var(--vscode-editor-background);
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
            border: 1px solid var(--vscode-panel-border);
        }

        .logo {
            font-size: 3em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #4285f4, #34a853, #fbbc04, #ea4335);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .title {
            font-size: 1.8em;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--vscode-foreground);
        }

        .subtitle {
            color: var(--vscode-descriptionForeground);
            margin-bottom: 30px;
            font-size: 1.1em;
            line-height: 1.5;
        }

        .google-signin-container {
            margin: 30px 0;
            display: flex;
            justify-content: center;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
            text-align: left;
        }

        .feature-item {
            padding: 15px;
            background: var(--vscode-editor-selectionBackground);
            border-radius: 8px;
            border: 1px solid var(--vscode-panel-border);
        }

        .feature-icon {
            font-size: 1.5em;
            margin-bottom: 8px;
            display: block;
        }

        .feature-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--vscode-foreground);
        }

        .feature-description {
            font-size: 0.9em;
            color: var(--vscode-descriptionForeground);
            line-height: 1.4;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-content {
            background: var(--vscode-editor-background);
            padding: 30px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid var(--vscode-panel-border);
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--vscode-progressBar-background);
            border-top: 4px solid #4285f4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #ffeaea;
            color: #d73a49;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border: 1px solid #f1b9b9;
            display: none;
        }

        .success-message {
            background: #e6ffed;
            color: #28a745;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border: 1px solid #b9f1c7;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="logo">🤖</div>
            <h1 class="title">Gemini VSCode Agent</h1>
            <p class="subtitle">
                Sign in with your Gmail account to unlock AI-powered coding assistance
            </p>

            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>

            <div class="google-signin-container">
                <div id="g_id_onload"
                     data-client_id="{{GOOGLE_CLIENT_ID}}"
                     data-callback="handleCredentialResponse"
                     data-auto_prompt="false">
                </div>
                
                <div class="g_id_signin"
                     data-type="standard"
                     data-size="large"
                     data-theme="outline"
                     data-text="sign_in_with"
                     data-shape="rectangular"
                     data-logo_alignment="left">
                </div>
            </div>

            <div class="features-grid">
                <div class="feature-item">
                    <span class="feature-icon">🧠</span>
                    <div class="feature-title">Smart Code Analysis</div>
                    <div class="feature-description">
                        Get intelligent insights and explanations for your code
                    </div>
                </div>

                <div class="feature-item">
                    <span class="feature-icon">💡</span>
                    <div class="feature-title">AI Suggestions</div>
                    <div class="feature-description">
                        Receive smart suggestions to improve your code quality
                    </div>
                </div>

                <div class="feature-item">
                    <span class="feature-icon">🔍</span>
                    <div class="feature-title">Code Explanation</div>
                    <div class="feature-description">
                        Understand complex code with detailed explanations
                    </div>
                </div>

                <div class="feature-item">
                    <span class="feature-icon">🚀</span>
                    <div class="feature-title">Auto Refactoring</div>
                    <div class="feature-description">
                        Automatically refactor and optimize your codebase
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <div>Authenticating with Google...</div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function showError(message) {
            const errorEl = document.getElementById('errorMessage');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            
            // Hide success message
            document.getElementById('successMessage').style.display = 'none';
        }

        function showSuccess(message) {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.style.display = 'block';
            
            // Hide error message
            document.getElementById('errorMessage').style.display = 'none';
        }

        function handleCredentialResponse(response) {
            try {
                showLoading();
                
                // Send the credential to the extension
                vscode.postMessage({
                    command: 'loginSuccess',
                    data: {
                        idToken: response.credential
                    }
                });
                
                showSuccess('Authentication successful! Redirecting...');
                
            } catch (error) {
                hideLoading();
                showError('Authentication failed: ' + error.message);
                
                vscode.postMessage({
                    command: 'loginError',
                    error: error.message
                });
            }
        }

        // Handle Google Sign-In errors
        window.addEventListener('error', (event) => {
            hideLoading();
            showError('An error occurred during authentication');
            
            vscode.postMessage({
                command: 'loginError',
                error: event.error ? event.error.message : 'Unknown error'
            });
        });

        // Handle messages from the extension
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.command) {
                case 'authError':
                    hideLoading();
                    showError(message.error);
                    break;
                case 'authSuccess':
                    hideLoading();
                    showSuccess('Login successful!');
                    break;
            }
        });

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Gemini VSCode Agent login page loaded');
        });
    </script>
</body>
</html>
