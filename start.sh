#!/bin/bash

echo "========================================"
echo "  Gemini VSCode Agent - Quick Start"
echo "========================================"
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js is available"

# Check if we're in the right directory
if [ ! -d "proxy-server" ]; then
    echo "❌ proxy-server directory not found"
    echo "Please run this script from the project root directory"
    exit 1
fi

if [ ! -d "extension" ]; then
    echo "❌ extension directory not found"
    echo "Please run this script from the project root directory"
    exit 1
fi

echo "✅ Project structure verified"

# Start proxy server
echo
echo "🚀 Starting Proxy Server..."
echo
cd proxy-server
node start.js
