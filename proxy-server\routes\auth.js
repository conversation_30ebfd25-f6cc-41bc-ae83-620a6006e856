const express = require('express');
const router = express.Router();
const tokenVerifier = require('../auth/verifyGoogleToken');

/**
 * POST /api/auth/login
 * Authenticate user with Google ID token
 */
router.post('/login', async (req, res) => {
  try {
    const { idToken } = req.body;

    if (!idToken) {
      return res.status(400).json({ 
        error: 'Missing ID token',
        code: 'MISSING_TOKEN'
      });
    }

    // Verify the Google ID token
    const userInfo = await tokenVerifier.verifyToken(idToken);
    
    // Create session
    const sessionId = tokenVerifier.createSession(userInfo);

    // Return success response
    res.json({
      success: true,
      sessionId,
      user: {
        id: userInfo.userId,
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture,
        domain: userInfo.domain
      },
      message: 'Authentication successful'
    });

    console.log(`✅ User authenticated: ${userInfo.email} (${userInfo.name})`);

  } catch (error) {
    console.error('Authentication error:', error.message);
    
    res.status(401).json({
      error: 'Authentication failed',
      message: error.message,
      code: 'AUTH_FAILED'
    });
  }
});

/**
 * POST /api/auth/verify
 * Verify session validity
 */
router.post('/verify', (req, res) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({ 
        error: 'Missing session ID',
        code: 'MISSING_SESSION'
      });
    }

    const session = tokenVerifier.validateSession(sessionId);

    if (!session) {
      return res.status(401).json({
        error: 'Invalid or expired session',
        code: 'INVALID_SESSION'
      });
    }

    res.json({
      valid: true,
      user: {
        id: session.userId,
        email: session.email,
        name: session.name,
        picture: session.picture,
        domain: session.domain
      },
      sessionInfo: {
        createdAt: session.createdAt,
        lastActivity: session.lastActivity
      }
    });

  } catch (error) {
    console.error('Session verification error:', error.message);
    
    res.status(500).json({
      error: 'Session verification failed',
      message: error.message,
      code: 'VERIFICATION_ERROR'
    });
  }
});

/**
 * POST /api/auth/logout
 * Invalidate user session
 */
router.post('/logout', (req, res) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({ 
        error: 'Missing session ID',
        code: 'MISSING_SESSION'
      });
    }

    tokenVerifier.invalidateSession(sessionId);

    res.json({
      success: true,
      message: 'Logout successful'
    });

    console.log(`🔓 Session invalidated: ${sessionId}`);

  } catch (error) {
    console.error('Logout error:', error.message);
    
    res.status(500).json({
      error: 'Logout failed',
      message: error.message,
      code: 'LOGOUT_ERROR'
    });
  }
});

/**
 * GET /api/auth/stats
 * Get authentication statistics (for monitoring)
 */
router.get('/stats', (req, res) => {
  try {
    const stats = tokenVerifier.getSessionStats();
    
    res.json({
      ...stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Stats error:', error.message);
    
    res.status(500).json({
      error: 'Failed to get statistics',
      message: error.message,
      code: 'STATS_ERROR'
    });
  }
});

/**
 * Middleware to verify session for protected routes
 */
const requireAuth = (req, res, next) => {
  try {
    const sessionId = req.headers['x-session-id'] || req.body.sessionId;

    if (!sessionId) {
      return res.status(401).json({
        error: 'Authentication required',
        code: 'NO_SESSION'
      });
    }

    const session = tokenVerifier.validateSession(sessionId);

    if (!session) {
      return res.status(401).json({
        error: 'Invalid or expired session',
        code: 'INVALID_SESSION'
      });
    }

    // Add user info to request
    req.user = {
      id: session.userId,
      email: session.email,
      name: session.name,
      picture: session.picture,
      domain: session.domain
    };

    req.sessionId = sessionId;
    next();

  } catch (error) {
    console.error('Auth middleware error:', error.message);
    
    res.status(500).json({
      error: 'Authentication check failed',
      message: error.message,
      code: 'AUTH_CHECK_ERROR'
    });
  }
};

module.exports = router;
module.exports.requireAuth = requireAuth;
