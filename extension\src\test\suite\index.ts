import * as fs from 'fs';
import * as path from 'path';

// Import Mocha properly
const Mocha = require('mocha');

export function run(): Promise<void> {
    // Create the mocha test
    const mocha = new Mocha({
        ui: 'tdd',
        color: true,
        timeout: 10000 // 10 seconds timeout for tests
    });

    const testsRoot = path.resolve(__dirname, '..');

    return new Promise((c, e) => {
        try {
            // Simple file discovery without glob
            const testFiles = findTestFiles(testsRoot);

            // Add files to the test suite
            testFiles.forEach((f: string) => mocha.addFile(f));

            // Run the mocha test
            mocha.run((failures: number) => {
                if (failures > 0) {
                    e(new Error(`${failures} tests failed.`));
                } else {
                    c();
                }
            });
        } catch (err) {
            console.error(err);
            e(err);
        }
    });
}

function findTestFiles(dir: string): string[] {
    const files: string[] = [];

    try {
        const items = fs.readdirSync(dir);

        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);

            if (stat.isDirectory()) {
                files.push(...findTestFiles(fullPath));
            } else if (item.endsWith('.test.js')) {
                files.push(fullPath);
            }
        }
    } catch (err) {
        console.warn(`Could not read directory ${dir}:`, err);
    }

    return files;
}
