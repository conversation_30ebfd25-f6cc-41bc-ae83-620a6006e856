# Gemini VSCode Agent

مساعد ذكي مدعوم بالذكاء الاصطناعي لـ VSCode باستخدام Google Gemini.

## المميزات

- 🧠 **تحليل الكود الذكي**: احصل على رؤى وتفسيرات ذكية لكودك
- 💡 **اقتراحات الذكاء الاصطناعي**: احصل على اقتراحات ذكية لتحسين جودة الكود
- 🔍 **شرح الكود**: فهم الكود المعقد مع شروحات مفصلة
- 🚀 **إعادة هيكلة تلقائية**: إعادة هيكلة وتحسين قاعدة الكود تلقائياً

## التثبيت والإعداد

### 1. إعداد Google OAuth Client ID

قبل استخدام Extension، تحتاج إلى إعداد Google OAuth Client ID. اتبع التعليمات في [SETUP.md](./SETUP.md).

### 2. تثبيت Extension

1. افتح VSCode
2. اذهب إلى Extensions (Ctrl+Shift+X)
3. ابحث عن "Gemini VSCode Agent"
4. انقر على Install

### 3. إعداد Client ID

1. اضغط `Ctrl+,` لفتح الإعدادات
2. ابحث عن "geminiAgent.googleClientId"
3. أدخل Google Client ID الخاص بك

## الاستخدام

### تسجيل الدخول

1. اضغط `Ctrl+Shift+P` لفتح Command Palette
2. ابحث عن "Gemini: Login with Gmail"
3. انقر عليه واتبع تعليمات تسجيل الدخول

### الأوامر المتاحة

- **Gemini: Login with Gmail** - تسجيل الدخول باستخدام Gmail
- **Gemini: Analyze Code** - تحليل الكود المحدد
- **Gemini: Get Suggestions** - الحصول على اقتراحات للتحسين
- **Gemini: Explain Code** - شرح الكود المحدد
- **Gemini: Refactor Code** - إعادة هيكلة الكود

### استخدام القائمة السياقية

انقر بالزر الأيمن على أي كود واختر من قائمة Gemini:
- Analyze with Gemini
- Get Suggestions
- Explain Code
- Refactor Code

## الإعدادات

- `geminiAgent.googleClientId`: Google OAuth Client ID
- `geminiAgent.autoApply`: تطبيق التغييرات تلقائياً بدون معاينة
- `geminiAgent.proxyUrl`: رابط الخادم الوسيط
- `geminiAgent.theme`: تفضيل المظهر (light/dark/auto)
- `geminiAgent.languageSupport`: اللغات المدعومة

## استكشاف الأخطاء

### مشكلة: زر Google لا يعمل
- تأكد من إعداد Google Client ID بشكل صحيح
- راجع [SETUP.md](./SETUP.md) للتعليمات المفصلة

### مشكلة: رسالة "Client ID not configured"
- أدخل Google Client ID في إعدادات VSCode
- أعد تشغيل VSCode بعد الإعداد

## التطوير

### متطلبات التطوير

- Node.js 16+
- npm
- VSCode

### تشغيل في وضع التطوير

```bash
cd extension
npm install
npm run compile
```

ثم اضغط F5 في VSCode لتشغيل Extension في وضع التطوير.

## الدعم

إذا واجهت أي مشاكل، يرجى:
1. التحقق من [SETUP.md](./SETUP.md) للإعداد الصحيح
2. فتح Developer Console (F12) للتحقق من الأخطاء
3. التحقق من Output panel في VSCode (View > Output > Gemini Agent)

## الترخيص

MIT License
